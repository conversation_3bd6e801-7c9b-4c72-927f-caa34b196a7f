{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": 4,
   "id": "480ac878",
   "metadata": {},
   "outputs": [],
   "source": [
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import pandas as pd\n",
    "# 前提：已生成data数组\n",
    "n1 = 168\n",
    "n2 = 168\n",
    "n3 = 74\n",
    "nt = 45\n",
    "nn = n1 * n2 * n3\n",
    "\n",
    "# 读取数据（与之前代码一致）\n",
    "file_path = r\"0100011.dyn.floatImage\"\n",
    "with open(file_path, \"rb\") as f:\n",
    "    data = np.fromfile(f, dtype=\">f4\", count=nn * nt)\n",
    "data = data.reshape(n1, n2, n3, nt, order=\"F\") # 注意Python里面矩阵排列是按行排，所以这里要设置order "
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 5,
   "id": "8da04c4d",
   "metadata": {},
   "outputs": [],
   "source": [
    "# 定义并注册NIH调色盘 (与Amide中一致)\n",
    "from matplotlib.colors import LinearSegmentedColormap\n",
    "nih_colors = [\n",
    "    (0.0, 0.0, 0.0),    # 黑色\n",
    "    (0.0, 0.0, 1.0),    # 蓝色\n",
    "    (0.0, 1.0, 1.0),    # 青色\n",
    "    (0.486, 0.988, 0.0),# 草绿色\n",
    "    (1.0, 1.0, 0.0),    # 黄色\n",
    "    (1.0, 0.0, 0.0)     # 红色\n",
    "]\n",
    "nih_cmap = LinearSegmentedColormap.from_list(\"nih\", nih_colors)\n",
    "plt.register_cmap(cmap=nih_cmap)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 6,
   "id": "1cda8cdb",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "image/png": "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********************************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*********************************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",
      "text/plain": [
       "<Figure size 800x600 with 2 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "# 示例1：绘制某一时间点的2D切片（沿n3轴的中间层）\n",
    "time_point = 44  # 选择最后一个时间点\n",
    "slice_idx = n3 // 2  # 选择n3维度的中间层\n",
    "# 注意：Python是从0开始计数，所以这里两个位置都要 减一\n",
    "slice_data = data[:, :, slice_idx-1, time_point-1]\n",
    "\n",
    "plt.figure(figsize=(8, 6))\n",
    "plt.imshow(slice_data, cmap=\"nih\")\n",
    "plt.title(f\"2D Slice at Time {time_point}, z={slice_idx}\")\n",
    "plt.colorbar(label=\"Intensity\")\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 8,
   "id": "72450306",
   "metadata": {},
   "outputs": [],
   "source": [
    "# read start time(ts) and end time(te) for each time frame. \n",
    "# and also the four time activity curves(tacs) you need to search - which is also ground truth\n",
    "GT = pd.read_csv('groundtruth.csv')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 9,
   "id": "a8172bf2",
   "metadata": {},
   "outputs": [],
   "source": [
    "ts = GT['ts']  # Extract the 'ts' column\n",
    "te = GT['te']  # Extract the 'te' column\n",
    "td = (ts + te) / 2 # mid time point of each time frame"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 13,
   "id": "d27311c3",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "image/png": "iVBORw0KGgoAAAANSUhEUgAAArwAAAIjCAYAAADhisjVAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/MnkTPAAAACXBIWXMAAA9hAAAPYQGoP6dpAABiCUlEQVR4nO3dd3hUVf7H8c+kTQikEkMSukgRpCOIjQ6Coq6sCugK4g9WBAvorrorVVewrooo6qJYaMoKiiISBUFdpEp3aRtsJCAgSUhIGDLn90eYIZNMeqYwvF/Pkwfm3Dv3nplvAp+cOfdcizHGCAAAAAhQQb7uAAAAAOBJBF4AAAAENAIvAAAAAhqBFwAAAAGNwAsAAICARuAFAABAQCPwAgAAIKAReAEAABDQCLwAAAAIaARewEuGDx+uRo0a+bobPvHVV1/JYrHoq6++qvBzu3fvru7du1d7nxC4Kvr99vTTT6tFixay2+2e7dh5bPDgwbrlllt83Q2cxwi8QBVYLJZyfVUm6HnT8ePHFR4eLovFoh9++KHSx3nllVc0Z86c6uuYGwcPHtTkyZO1ZcsWjxx/y5Ytuv3221W/fn1ZrVbFxcWpd+/eeuutt5Sfn++Rc/oDT7+v/iozM1NPPfWUHn74YQUFnf0vMTc3V9OmTVPLli0VERGhunXr6uabb9bOnTtLPd7IkSNlsVh03XXXValfe/fu1eDBg1WvXj1FRESoRYsWmjp1qnJyckp8zvHjx5WQkCCLxaJFixZV+txvvPGGunXrpjp16shqtapx48a68847deDAAZf95syZU+q/e3PnznXu+/DDD+vf//63tm7dWul+AVUR4usOAOeyd9991+XxO++8o5SUlGLtF198sd544w2/HUH64IMPZLFYlJiYqLlz5+qJJ56o1HFeeeUVxcfHa/jw4S7tV199tU6ePKmwsLAKH3PFihUujw8ePKgpU6aoUaNGateuXaX6WZJ//etfuvvuu1WnTh396U9/UtOmTZWVlaUvv/xSd911l9LS0vS3v/2tWs/pLzz5vvqzN998U6dPn9aQIUNc2m+77TZ9/PHHGjlypDp06KCDBw9q5syZ6tq1q7Zv366GDRsWO9bGjRs1Z84chYeHV6lPP//8szp37qzo6GiNHTtWcXFxWrt2rSZNmqRNmzbpo48+cvu8iRMnlhqIy+v7779X48aNdf311ys2Nlapqal644039Mknn2jr1q1KTk6WVPBzXfTfOkn65z//qa1bt6pXr17Otvbt26tTp0567rnn9M4771S5j0CFGQDVZsyYMeZc/LG6+uqrzU033WTGjRtnGjduXOnjtGrVynTr1q36OubGhg0bjCTz1ltvVetx165da4KDg82VV15pMjMz3Z63us554sSJajlOdfLU++oLq1atMpLMqlWryty3TZs25vbbb3dp++WXX4wk89BDD7m0r1y50kgyzz//fLHj2O1207VrVzNixAjTsGFDc+2111a6///4xz+MJLNjxw6X9jvuuMNIMseOHSv2nO3bt5uQkBAzdepUI8l88MEHlT6/Oxs3bjSSzLRp00rdLycnx0RGRpo+ffoU2/bss8+amjVrmqysrGrtG1AeTGkAvKToHN4DBw7IYrHo2Wef1cyZM3XhhRcqIiJCffv21c8//yxjjB5//HHVq1dPNWrU0A033KBjx44VO+5nn32mq666SjVr1lRkZKSuvfbaMj92Leynn37S119/rcGDB2vw4MFKTU3Vf/7zH7f7vvfee+rcubMiIiIUGxurq6++2jkC26hRI+3cuVOrV692fqTpmHtbdE7l2LFjVatWLbejUUOGDFFiYqJz+kDhObxfffWVLr30UknSnXfe6TzPnDlzNGnSJIWGhuq3334rdsxRo0YpJiZGubm5Jb4PU6ZMcX4MGxkZWWx7p06dnCPXJc0RddS08LSO4cOHq1atWtq/f78GDBigyMhI3XbbbRV6D6TK1/nYsWN66KGH1Lp1a9WqVUtRUVHq37+/y0fLpb2v7ixatEgWi0WrV68utu21116TxWLRjh07nG0rV6509j0mJkY33HCDy9SZt956SxaLRW+++abLsZ588klZLBYtW7bM2fbf//5Xf/zjHxUXF6fw8HB16tRJH3/8cZnvgzupqanatm2bevfu7dKelZUlSapTp45Le1JSkiSpRo0axY717rvvaseOHfrHP/5Rqb4UlpmZWeL5g4KC3H5Scv/99+sPf/iDrrrqqiqf3x3Hv13Hjx8vdb+lS5cqKytLt912W7Ftffr0UXZ2tlJSUjzQQ6AMvk7cQCApbYR32LBhpmHDhs7HqampRpJp166dadmypXn++efNY489ZsLCwsxll11m/va3v5nLL7/cvPTSS+a+++4zFovF3HnnnS7HfOedd4zFYjHXXHONmTFjhnnqqadMo0aNTExMjElNTS1Xn6dPn25q1aplcnJyjDHGNGnSxNxzzz3F9ps8ebKRZC6//HLzzDPPmBdffNEMHTrUPPzww8YYYxYvXmzq1atnWrRoYd59913z7rvvmhUrVhhjio+4rVmzxkgy77//vss5srOzTc2aNc2YMWOcbd26dXOOGqenpztHsEaNGuU8z/79+83evXuNJDNjxgyXY+bl5ZnY2FgzYsSIEt+D7OxsExoaanr27Fmu96ykEURHTQuPkg4bNsxYrVbTpEkTM2zYMDNr1izzzjvvVOg9qEqdN2zYYJo0aWIeeeQR89prr5mpU6eaunXrmujoaPPrr78aY0p/X93JyckxtWrVcvt90qNHD9OqVSvn45SUFBMSEmKaNWtmnn76aTNlyhQTHx9vYmNjXfp+3XXXmejoaPPTTz8ZY4zZtm2bCQsLM3fddZdznx07dpjo6GjTsmVL89RTT5mXX37ZXH311cZisZgPP/zQuV95R3jfe+89I8ls27bNpf3UqVOmXr16JjEx0Xz88cfm559/NuvWrTPdunUzjRs3Nr///rvL/pmZmSYxMdE5+lnVEd7PPvvMSDLXX3+9+f77781PP/1kFixYYKKioswDDzxQbP/333/fhIeHm9TUVOdrr44R3iNHjphDhw6ZDRs2mIEDBxpJzp/pklx//fWmRo0abj8lsdlspkaNGubBBx+sct+AiiLwAtWoMoH3ggsuMMePH3e2P/roo0aSadu2rbHZbM72IUOGmLCwMJObm2uMMSYrK8vExMSYkSNHupwnPT3dREdHF2svSevWrc1tt93mfPy3v/3NxMfHu5x77969JigoyPzhD38w+fn5Ls+32+3Ov5c0paFoALHb7aZu3bpm0KBBLvu9//77RpJZs2aNs61w4DWm9I/eu3btarp06eLS9uGHH5YZfrZu3Wokmfvvv7/EfUp7PQ4lBV5J5pFHHnHZt7zvQVXrnJubW6xmqampxmq1mqlTpzrbKjqlYciQISYhIcGcPn3a2ZaWlmaCgoJcjtuuXTuTkJBgjh496mzbunWrCQoKMnfccYfLc+Pi4kyfPn1MXl6ead++vWnQoIHJyMhw7tOrVy/TunVr58+AMQXv4+WXX26aNm3qbCtv4H3ssceMJLcfsa9bt840adLESHJ+dezY0aSlpRXb96GHHjKNGzd29quqgdcYYx5//HFTo0YNl/P//e9/L7ZfTk6OadCggXn00UeNMaZaA6/VanWeu3bt2uall14qdf+jR4+asLAwc8stt5S4T7NmzUz//v2r3DegopjSAPjYzTffrOjoaOfjLl26SJJuv/12hYSEuLSfOnVKv/76qyQpJSVFx48f15AhQ3TkyBHnV3BwsLp06aJVq1aVee5t27Zp+/btLhfsOI73+eefO9uWLFkiu92uiRMnulzJLhWsVFFRFotFN998s5YtW6YTJ0442xcuXKi6devqyiuvrPAxJemOO+7QunXrtH//fmfb3LlzVb9+fXXr1q3E5zk+QnY3laG6jB492uVxed+DqtbZarU6a5afn6+jR4+qVq1aat68uTZv3lzp13Prrbfq8OHDLtM6Fi1aJLvdrltvvVWSlJaWpi1btmj48OGKi4tz7temTRv16dPHZapCYmKiZs6cqZSUFF111VXasmWL3nzzTUVFRUkqmJqxcuVK3XLLLcrKynK+D0ePHlW/fv20d+9e589GeR09elQhISGqVatWsW2xsbFq166dHnnkES1ZskTPPvusDhw4oJtvvtllasyePXv04osv6plnnpHVaq3Q+UvTqFEjXX311Xr99df173//WyNGjNCTTz6pl19+2WW/6dOny2azeeRiys8++0zLli3Tc889pwYNGig7O7vU/RctWqRTp065nc7gEBsbqyNHjlR3V4EysUoD4GMNGjRweewIv/Xr13fb/vvvv0sqWLZIknr27On2uI6gcPLkSWVkZLhsS0xMlFQwJ7dmzZq68MILtW/fPklSeHi4GjVqpLlz5+raa6+VJO3fv19BQUFq2bJl5V6kG7feeqteeOEFffzxxxo6dKhOnDihZcuW6c9//nOlQrTjmA888IDmzp2riRMnKiMjQ5988onGjRtX6jEd75Vj7mZ1CwkJUb169dz2t6z3oLx1LondbteLL76oV155RampqS7zgmvXrl3Zl6RrrrlG0dHRWrhwofNq/IULF6pdu3Zq1qyZJOnHH3+UJDVv3rzY8y+++GJ9/vnnys7OVs2aNSUVrNX63nvv6dNPP9WoUaNcrvLft2+fjDGaMGGCJkyY4LZPhw8fVt26dSv9mhwyMjJ01VVX6S9/+YsefPBBZ3unTp3UvXt3vfXWW85fYO6//35dfvnlGjRoUJXP67BgwQKNGjVKe/bscX7f3HTTTbLb7Xr44Yc1ZMgQ1a5dWwcOHNAzzzyjmTNnug3tVdWjRw9JUv/+/XXDDTfokksuUa1atTR27Fi3+8+dO1dxcXHq379/icc0xlT65xuoCgIv4GPBwcEVajfGSJJzibN3333XGWALc4wOL1y4UHfeeWexYxhjNH/+fGVnZ7sNsocPH9aJEyc88h+pJF122WVq1KiR3n//fQ0dOlRLly7VyZMnnaODlREbG6vrrrvOGXgXLVqkvLw83X777aU+76KLLlJISIi2b99ervOU9B92Sev0Fh5lLaw870F561ySJ598UhMmTNCIESP0+OOPKy4uTkFBQXrggQeqtEye1WrVjTfeqMWLF+uVV17RoUOH9O233+rJJ5+s9DGPHj2qjRs3SpJ27dolu93ufN8cfX3ooYfUr18/t8+/6KKLKnS+2rVr6/Tp08rKynIZ3f/3v/+tQ4cO6frrr3fZv1u3boqKitK3336r0aNHa+XKlVq+fLk+/PBDlzVqT58+rZMnT+rAgQOKi4sr85eSol555RW1b9++2C9J119/vebMmaPvv/9evXv31sSJE1W3bl11797def709HRJ0m+//aYDBw6oQYMGbr/3KqpJkyZq37695s6d6zbwOi5+HTVqlEJDQ0s8zu+//66mTZtWuT9ARRF4gXNUkyZNJEkJCQnFrjIvrF+/fm6vil69erV++eUXTZ06VRdffLHLtt9//12jRo3SkiVLdPvtt6tJkyay2+3atWtXqWu0VnTk5pZbbtGLL76ozMxMLVy4UI0aNdJll11W6nPKOscdd9yhG264QRs2bNDcuXPVvn17tWrVqtTnREREqGfPnlq5cqV+/vnnYqPrRcXGxkoqfsW6Y0SzIsp6D8pb55IsWrRIPXr00OzZs13ajx8/rvj4eOfjyoy63XrrrXr77bf15Zdf6ocffpAxxiWsO9aq3b17d7Hn/ve//1V8fLxzdFeSxowZo6ysLE2bNk2PPvqoXnjhBY0fP16SdOGFF0qSQkNDK/U+uNOiRQtJBas1tGnTxtl+6NAhScV/gTHGKD8/X6dPn5ZUEPKkgtHXon799Vc1btxY//znP/XAAw9UqF+HDh1yfo8VZrPZJMnl/Pv27XO+N4Xdc889kgp+lmNiYip0/pKcPHlSeXl5brfNnz9fxphSpzOcPn1aP//8c7FfJABvYA4vcI7q16+foqKi9OSTTzr/IyzMsTxXUlKSevfu7fIlnZ3O8Je//EV//OMfXb5Gjhyppk2bOu+UdOONNyooKEhTp04tNiroGHGWpJo1a5a5bFFht956q/Ly8vT2229r+fLl5br1qCMglXSe/v37Kz4+Xk899ZRWr15d5uiuw6RJk2SM0Z/+9CeXObUOmzZt0ttvvy2pIMgFBwdrzZo1Lvu88sor5TpXYWW9B+Wtc0mCg4NdaiQV3Gik6HzXst5Xd3r37q24uDgtXLhQCxcuVOfOndW4cWPn9qSkJLVr105vv/22y3F37NihFStWaMCAAc62RYsWaeHChZo+fboeeeQRDR48WI899pj27NkjqSDwd+/eXa+99prS0tKK9aWs98Gdrl27SpJzVNnBMSVjwYIFLu0ff/yxsrOz1b59e0kF00wWL15c7OuCCy5Qp06dtHjxYg0cOLDC/WrWrJm+//5752t3mD9/voKCgpzh/Iknnih27scff1yS9Ne//lWLFy92+YWiPE6fPu2cNlXY+vXrtX37dnXq1Mnt8+bNm6cGDRqUOv9+165dys3N1eWXX16hPgHVwkcXywEBqTKrNDzzzDMu+5V0lfVbb71lJJkNGzY42+bOnWuCgoLMJZdcYp544gnz2muvmb///e+mXbt2LstaFZWbm2tiYmLMjTfeWOI+Dz74oAkJCTGHDh0yxhgzYcIE57Jkzz77rJkxY4a54447XFYfuOeee4zFYjGPP/64mT9/vvnyyy9dXpO7q+YvuugiExkZaSSZTZs2FdtedJWGU6dOmZiYGNO8eXPzr3/9y8yfP9/873//c3nO2LFjjSQTHBxsDh48WOJrLGrWrFkmKCjI1K1b1zzyyCNm9uzZ5oUXXjA33nijCQoKMk8++aRz38GDB5uQkBAzfvx4M3PmTNO/f3/TsWNHt6s01KxZs9TzlvUeVLbOxhgzceJEI8kMHz7cvP766+bee+81cXFx5sILL6zw++rO//3f/5latWoZi8VinnvuuWLbHcuStWjRwjzzzDNm6tSp5oILLjCxsbHO4x86dMjEx8ebHj16OFf9OHLkiKlTp47p2rWrc5WJnTt3mtjYWFO7dm3zyCOPmNdff908/vjjZsCAAaZNmzbOc1bkxhOXXHKJGTJkiEtbXl6eadWqlbFYLGb48OFm1qxZ5qGHHjLh4eEmKSnJ/Pbbb6Ues6RVGhwrdpS1lNzq1atNcHCwSUhIMFOnTnV+f0ky//d//1fqc0tbpUFSmTeG+f33303NmjXNiBEjzHPPPWdmzZplxowZYyIiIkxcXJzZs2dPseds377d7UokRT377LMmIiLC7ZJlgKcReIFq5O3A69i/X79+Jjo62oSHh5smTZqY4cOHm40bN5bYz3//+99Gkpk9e3aJ+3z11VdGknnxxRedbW+++aZp3769sVqtJjY21nTr1s2kpKQ4t6enp5trr73WGd4c/7mWFkD+/ve/G0nmoosuctuPooHXGGM++ugj07JlSxMSEuJ2Ka3169cbSaZv374lvr6SbNq0yQwdOtQkJyeb0NBQExsba3r16mXefvttl+W9fvvtNzNo0CATERFhYmNjzZ///GezY8eOSgXest4DYypXZ2MKfrl58MEHTVJSkqlRo4a54oorzNq1ayv1vrqTkpJiJBmLxWJ+/vlnt/t88cUX5oorrjA1atQwUVFRZuDAgWbXrl3O7TfddJOJjIw0Bw4cKNYfSeapp55ytu3fv9/ccccdJjEx0YSGhpq6deua6667zixatMjlvSpv4H3++edd1qF2OHbsmBk3bpxp1qyZsVqtJj4+3gwePLhcvwSUFHgHDRpkatSoUWwdX3fWrVtn+vfv73ydzZo1M//4xz9clgt0p6R/P7KysowkM3jw4FKfn5eXZ+6//37Tpk0bExUVZUJDQ03Dhg3NXXfdVWJQf+SRR9yuZ1xUly5dit3VDvAWizFFPusCgHPc1q1b1a5dO73zzjv605/+5OvuwI9lZGTowgsv1NNPP6277rrLo+eqU6eO7rjjDj3zzDMePY87y5Yt03XXXaetW7eqdevWXj//li1b1KFDB23evLnU6wAATyHwAgg4Y8eO1dtvv6309PQKz2HE+eepp57SW2+9pV27dlXLigbu7Ny5U127dtX//vc/l4sFveUvf/mLfv31V82bN8/r55YKlpyz2+16//33fXJ+gMALIGAsXbpUu3bt0oQJEzR27Fg9//zzvu4SAMAPEHgBBIxGjRrp0KFD6tevn959912P3jkNAHDuIPACAAAgoLEOLwAAAAIagRcAAAABjVsLq+Ae7QcPHlRkZGSlbq8JAAAAzzLGKCsrS8nJyRVeUYXAK+ngwYOqX7++r7sBAACAMvz888+qV69ehZ5D4JWcV3L//PPPioqK8ui5bDabVqxYob59+yo0NNSj50LpqIV/oA7+gTr4B+rgH6iDfyhah8zMTNWvX79SK/AQeCXnNIaoqCivBN6IiAhFRUXxQ+Rj1MI/UAf/QB38A3XwD9TBP5RUh8pMP+WiNQAAAAQ0Ai8AAAACGoEXAAAAAY3ACwAAgIBG4AUAAEBAI/ACAAAgoBF4AQAAENAIvAAAAAhoBF4AAAAENAIvAAAAAhqBFwAAAAGNwAsAAICARuAFAABAQAvxdQfOR3YjrUs9pqM5p5UQGa7OjeMUHGTxdbcAAAACEoHXyz7feUhTNgfr+HcbnW1J0eGaNLClrrkkyYc9AwAACExMafCi5TvSdO+CrTp+yrU9PSNXo9/brOU70nzTMQAAgABG4PWSfLvRlKW7ZCRJrtMXzJk/pyzdpXy7EQAAAKoPgddL1qceU1pGbonbjaS0jFytTz3mvU4BAACcBwi8XnI4q+SwW5n9AAAAUD4EXi9JiAyv1v0AAABQPgReL+ncOE5J0eEqafExiwpWa+jcOM6b3QIAAAh4BF4vCQ6yaNLAlmceuV6Y5gjBkwa2ZD1eAACAakbg9aJrLknSjMFtFRPm2p4YHa5Xb+/AOrwAAAAewI0nvKxfqzqyHcjXuO/OvvXfPNyTkV0AAAAPYYTXB4pmW8IuAACA5xB4AQAAENAIvAAAAAhoBF4AAAAENAIvAAAAAhqBFwAAAAGNwAsAAICARuAFAABAQPNp4F2zZo0GDhyo5ORkWSwWLVmyxGW7xWJx+/XMM88492nUqFGx7dOnT/fyKwEAAIC/8mngzc7OVtu2bTVz5ky329PS0ly+3nzzTVksFg0aNMhlv6lTp7rsd++993qj+wAAADgH+PTWwv3791f//v1L3J6YmOjy+KOPPlKPHj104YUXurRHRkYW2xcAAACQfBx4K+LQoUP69NNP9fbbbxfbNn36dD3++ONq0KCBhg4dqnHjxikkpOSXlpeXp7y8POfjzMxMSZLNZpPNZqv+zhfi7viePifcc7zvvP++RR38A3XwD9TBP1AH/1C0DlWpxzkTeN9++21FRkbqpptucmm/77771KFDB8XFxek///mPHn30UaWlpen5558v8VjTpk3TlClTirWvWLFCERER1d73sixbtszr58RZKSkpvu4CRB38BXXwD9TBP1AH/+CoQ05OTqWPYTHGmOrqUFVYLBYtXrxYN954o9vtLVq0UJ8+fTRjxoxSj/Pmm2/qz3/+s06cOCGr1ep2H3cjvPXr19eRI0cUFRVV6ddQHjabTSkpKbp/7dnfNfY+3tej54R7jlr06dNHoaGhvu7OeYs6+Afq4B+og3+gDv6haB0yMzMVHx+vjIyMCue1c2KE9+uvv9bu3bu1cOHCMvft0qWLTp8+rQMHDqh58+Zu97FarW7DcGhoqE++sflh8i1f1R2uqIN/oA7+gTr4B+rgHxx1qEotzol1eGfPnq2OHTuqbdu2Ze67ZcsWBQUFKSEhwQs9AwAAgL/z6QjviRMntG/fPufj1NRUbdmyRXFxcWrQoIGkgukGH3zwgZ577rliz1+7dq3WrVunHj16KDIyUmvXrtW4ceN0++23KzY21muvAwAAAP7Lp4F348aN6tGjh/Px+PHjJUnDhg3TnDlzJEkLFiyQMUZDhgwp9nyr1aoFCxZo8uTJysvLU+PGjTVu3DjncQAAAACfBt7u3burrGvmRo0apVGjRrnd1qFDB3333Xee6BoAAAACxDkxhxcAAACoLAIvAAAAAhqBFwAAAAGNwAsAAICARuAFAABAQCPwAgAAIKAReAEAABDQCLwAAAAIaAReAAAABDQCLwAAAAIagRcAAAABjcALAACAgEbgBQAAQEAj8AIAACCgEXgBAAAQ0Ai8AAAACGgEXgAAAAQ0Ai8AAAACGoEXAAAAAY3ACwAAgIBG4AUAAEBAI/ACAAAgoBF4AQAAENAIvAAAAAhoBF4AAAAENAIvAAAAAhqBFwAAAAGNwAsAAICARuAFAABAQCPwAgAAIKAReAEAABDQCLwAAAAIaAReAAAABDQCLwAAAAIagRcAAAABjcALAACAgEbgBQAAQEAj8AIAACCgEXgBAAAQ0Ai8AAAACGgEXgAAAAQ0Ai8AAAACGoEXAAAAAY3ACwAAgIBG4AUAAEBAI/ACAAAgoBF4AQAAENB8GnjXrFmjgQMHKjk5WRaLRUuWLHHZPnz4cFksFpeva665xmWfY8eO6bbbblNUVJRiYmJ011136cSJE158FQAAAPBnPg282dnZatu2rWbOnFniPtdcc43S0tKcX/Pnz3fZftttt2nnzp1KSUnRJ598ojVr1mjUqFGe7joAAADOESG+PHn//v3Vv3//UvexWq1KTEx0u+2HH37Q8uXLtWHDBnXq1EmSNGPGDA0YMEDPPvuskpOTq73PAAAAOLf4NPCWx1dffaWEhATFxsaqZ8+eeuKJJ1S7dm1J0tq1axUTE+MMu5LUu3dvBQUFad26dfrDH/7g9ph5eXnKy8tzPs7MzJQk2Ww22Ww2D74auT2+p88J9xzvO++/b1EH/0Ad/AN18A/UwT8UrUNV6uHXgfeaa67RTTfdpMaNG2v//v3629/+pv79+2vt2rUKDg5Wenq6EhISXJ4TEhKiuLg4paenl3jcadOmacqUKcXaV6xYoYiIiGp/HWVZtmyZ18+Js1JSUnzdBYg6+Avq4B+og3+gDv7BUYecnJxKH8OvA+/gwYOdf2/durXatGmjJk2a6KuvvlKvXr0qfdxHH31U48ePdz7OzMxU/fr11bdvX0VFRVWpz2Wx2WzFfoAGDBjg0XPCPUct+vTpo9DQUF9357xFHfwDdfAP1ME/UAf/ULQOjk/kK8OvA29RF154oeLj47Vv3z716tVLiYmJOnz4sMs+p0+f1rFjx0qc9ysVzAu2Wq3F2kNDQ33yjc0Pk2/5qu5wRR38A3XwD9TBP1AH/+CoQ1VqcU6tw/vLL7/o6NGjSkpKkiR17dpVx48f16ZNm5z7rFy5Una7XV26dPFVNwEAAOBHfDrCe+LECe3bt8/5ODU1VVu2bFFcXJzi4uI0ZcoUDRo0SImJidq/f7/++te/6qKLLlK/fv0kSRdffLGuueYajRw5UrNmzZLNZtPYsWM1ePBgVmgAAACAJB+P8G7cuFHt27dX+/btJUnjx49X+/btNXHiRAUHB2vbtm26/vrr1axZM911113q2LGjvv76a5fpCHPnzlWLFi3Uq1cvDRgwQFdeeaVef/11X70kAAAA+BmfjvB2795dxpgSt3/++edlHiMuLk7z5s2rzm4BAAAggJxTc3gBAACAiiLwAgAAIKAReAEAABDQCLwAAAAIaAReAAAABDQCLwAAAAIagRcAAAABjcALAACAgEbgBQAAQEAj8AIAACCgEXgBAAAQ0Ai8AAAACGgEXgAAAAQ0Ai8AAAACGoEXAAAAAY3ACwAAgIBG4AUAAEBAI/ACAAAgoBF4AQAAENAIvAAAAAhoBF4AAAAENAIvAAAAAhqBFwAAAAGNwAsAAICARuAFAABAQCPwAgAAIKAReAEAABDQCLwAAAAIaAReAAAABDQCLwAAAAIagRcAAAABjcALAACAgEbgBQAAQEAj8AIAACCgEXgBAAAQ0Ai8AAAACGgEXgAAAAQ0Ai8AAAACGoEXAAAAAY3ACwAAgIBG4AUAAEBAI/ACAAAgoBF4AQAAENAIvAAAAAhoBF4AAAAENAIvAAAAAppPA++aNWs0cOBAJScny2KxaMmSJc5tNptNDz/8sFq3bq2aNWsqOTlZd9xxhw4ePOhyjEaNGslisbh8TZ8+3cuvBAAAAP7Kp4E3Oztbbdu21cyZM4tty8nJ0ebNmzVhwgRt3rxZH374oXbv3q3rr7++2L5Tp05VWlqa8+vee+/1RvcBAABwDgjx5cn79++v/v37u90WHR2tlJQUl7aXX35ZnTt31k8//aQGDRo42yMjI5WYmOjRvgIAAODc5NPAW1EZGRmyWCyKiYlxaZ8+fboef/xxNWjQQEOHDtW4ceMUElLyS8vLy1NeXp7zcWZmpqSCaRQ2m80jfXdwd3xPnxPuOd533n/fog7+gTr4B+rgH6iDfyhah6rUw2KMMdXSqyqyWCxavHixbrzxRrfbc3NzdcUVV6hFixaaO3eus/35559Xhw4dFBcXp//85z969NFHdeedd+r5558v8VyTJ0/WlClTirXPmzdPERERVX4t5XH/2rOB/MWup71yTgAAgHNVTk6Ohg4dqoyMDEVFRVXouedE4LXZbBo0aJB++eUXffXVV6W+yDfffFN//vOfdeLECVmtVrf7uBvhrV+/vo4cOVLhN7CibDabUlJSXALv3sf7evSccM9Riz59+ig0NNTX3TlvUQf/QB38A3XwD9TBPxStQ2ZmpuLj4ysVeP1+SoPNZtMtt9yiH3/8UStXrizzBXbp0kWnT5/WgQMH1Lx5c7f7WK1Wt2E4NDTUJ9/Y/DD5lq/qDlfUwT9QB/9AHfwDdfAPjjpUpRZ+HXgdYXfv3r1atWqVateuXeZztmzZoqCgICUkJHihhwAAAPB3Pg28J06c0L59+5yPU1NTtWXLFsXFxSkpKUl//OMftXnzZn3yySfKz89Xenq6JCkuLk5hYWFau3at1q1bpx49eigyMlJr167VuHHjdPvttys2NtZXLwsAAAB+xKeBd+PGjerRo4fz8fjx4yVJw4YN0+TJk/Xxxx9Lktq1a+fyvFWrVql79+6yWq1asGCBJk+erLy8PDVu3Fjjxo1zHgcAAADwaeDt3r27Srtmrqzr6Tp06KDvvvuuursFAACAAOLTO62hgJ8slAEAABCQCLx+gLwLAADgOQReP0DeBQAA8BwCrx9gSgMAAIDnEHj9AHEXAADAcwi8foABXgAAAM8h8PoBwxgvAACAxxB4AQAAENAIvH6AKQ0AAACeQ+AFAABAQCPw+gFGeAEAADyHwOsHuGgNAADAcwi8foARXgAAAM8h8PoB8i4AAIDnEHj9ALcWBgAA8BwCrw8UzbfEXQAAAM8h8AIAACCgEXh9oOiILjMaAAAAPIfA6w8IvAAAAB5D4PUDrMMLAADgOQReH2BKAwAAgPcQeH2BVRoAAAC8hsDrA8VHeIm8AAAAnkLg9QPEXQAAAM8h8PoBBngBAAA8h8DrA8WmNDDGCwAA4DEEXgAAAAQ0Aq8PFJvCwAAvAACAxxB4/QB5FwAAwHMIvD7AjScAAAC8h8DrB7hoDQAAwHMIvH6AEV4AAADPIfD6ANesAQAAeA+B1w9wa2EAAADPIfD6QpF8S94FAADwHAKvD5BvAQAAvIfACwAAgIBG4PUB1uEFAADwHgKvH2AdXgAAAM8h8PpA0RFdRngBAAA8h8DrB8i7AAAAnkPg9QOswwsAAOA5BF4f4E5rAAAA3kPg9QMM8AIAAHgOgdcvkHgBAAA8hcDrA8RbAAAA76lw4J00aZJ+/PFHT/TlvMGyZAAAAN5T4cD70UcfqUmTJurVq5fmzZunvLy8Sp98zZo1GjhwoJKTk2WxWLRkyRKX7cYYTZw4UUlJSapRo4Z69+6tvXv3uuxz7Ngx3XbbbYqKilJMTIzuuusunThxotJ98gXyLgAAgOdUOPBu2bJFGzZsUKtWrXT//fcrMTFRo0eP1oYNGyp88uzsbLVt21YzZ850u/3pp5/WSy+9pFmzZmndunWqWbOm+vXrp9zcXOc+t912m3bu3KmUlBR98sknWrNmjUaNGlXhvvgSI7wAAACeU6k5vO3bt9dLL72kgwcPavbs2frll190xRVXqE2bNnrxxReVkZFRruP0799fTzzxhP7whz8U22aM0QsvvKDHHntMN9xwg9q0aaN33nlHBw8edI4E//DDD1q+fLn+9a9/qUuXLrryyis1Y8YMLViwQAcPHqzMS/OK4suSkXgBAAA8JaQqTzbGyGaz6dSpUzLGKDY2Vi+//LImTJigN954Q7feemulj52amqr09HT17t3b2RYdHa0uXbpo7dq1Gjx4sNauXauYmBh16tTJuU/v3r0VFBSkdevWuQ3SkpSXl+cyFSMzM1OSZLPZZLPZKt3n8nB3fJvttMfPi+Ic7znvvW9RB/9AHfwDdfAP1ME/FK1DVepRqcC7adMmvfXWW5o/f76sVqvuuOMOzZw5UxdddJEkacaMGbrvvvuqFHjT09MlSXXq1HFpr1OnjnNbenq6EhISXLaHhIQoLi7OuY8706ZN05QpU4q1r1ixQhEREZXuc2V9/fXX+l9Nr58WZ6SkpPi6CxB18BfUwT9QB/9AHfyDow45OTmVPkaFA2/r1q313//+V3379tXs2bM1cOBABQcHu+wzZMgQ3X///ZXulKc9+uijGj9+vPNxZmam6tevr759+yoqKsqj57bZbPrgE9cfoCuuvFItkzx7XhRns9mUkpKiPn36KDQ01NfdOW9RB/9AHfwDdfAP1ME/FK2D4xP5yqhw4L3llls0YsQI1a1bt8R94uPjZbfbK90pSUpMTJQkHTp0SElJSc72Q4cOqV27ds59Dh8+7PK806dP69ixY87nu2O1WmW1Wou1h4aG+uQbOzg4hB8oH/JV3eGKOvgH6uAfqIN/oA7+wVGHqtSiwhetOebqFnXy5ElNnTq10h0pqnHjxkpMTNSXX37pbMvMzNS6devUtWtXSVLXrl11/Phxbdq0ybnPypUrZbfb1aVLl2rrCwAAAM5dFQ68U6ZMcbvObU5Ojtt5saU5ceKEtmzZoi1btkgquFBty5Yt+umnn2SxWPTAAw/oiSee0Mcff6zt27frjjvuUHJysm688UZJ0sUXX6xrrrlGI0eO1Pr16/Xtt99q7NixGjx4sJKTkyv60ryGNRkAAAC8p8JTGowxslgsxdq3bt2quLi4Ch1r48aN6tGjh/OxY17tsGHDNGfOHP31r39Vdna2Ro0apePHj+vKK6/U8uXLFR4e7nzO3LlzNXbsWPXq1UtBQUEaNGiQXnrppYq+LK8qtiwZCRgAAMBjyh14Y2NjZbFYZLFY1KxZM5fQm5+frxMnTujuu++u0Mm7d+8uU0ras1gsmjp1aqlTJeLi4jRv3rwKndffsA4vAACA55Q78L7wwgsyxmjEiBGaMmWKoqOjndvCwsLUqFEj59xaVAwjvAAAAJ5T7sA7bNgwSQUXk11++eVctVgFRQMueRcAAMBzyhV4MzMznevTtm/fXidPntTJkyfd7uvpdWwDUWnTOgAAAFA15Qq8sbGxSktLU0JCgmJiYtxetOa4mC0/P7/aOxnoiLsAAACeU67Au3LlSucKDCtXrnQbeFF+rNIAAADgPeUKvN26dXP+vXv37p7qy3mMxAsAAOApFb7xRNOmTTV58mTt3bvXE/05LzHCCwAA4DkVDrz33HOPPv30U7Vo0UKXXnqpXnzxRaWnp3uibwGLgAsAAOA9FQ6848aN04YNG/TDDz9owIABmjlzpurXr6++ffvqnXfe8UQfA06xObw+6QUAAMD5ocKB16FZs2aaMmWK9uzZo6+//lq//fab7rzzzurs23mDEV8AAADPKfeNJ9xZv3695s2bp4ULFyozM1M333xzdfXrvMI6vAAAAJ5T4cC7Z88ezZ07V/Pnz1dqaqp69uypp556SjfddJNq1arliT4GHKY0AAAAeE+FA6/jYrUxY8Zo8ODBqlOnjif6dV5hgBcAAMBzKhx4d+/eraZNm3qiL+ctwxgvAACAx1RqHV5UTbERXfIuAACAx5RrhDcuLk579uxRfHy8YmNjS7218LFjx6qtc+cL8i4AAIDnlCvw/vOf/1RkZKTz76UFXgAAAMCflCvwDhs2zPn34cOHe6ov541iMxoY4gUAAPCYCs/hDQ4O1uHDh4u1Hz16VMHBwdXSqUBXfAoviRcAAMBTKhx4S7pJQl5ensLCwqrcofMRI7wAAACeU+5lyV566SVJksVi0b/+9S+Xm0zk5+drzZo1atGiRfX3MBCZUh8CAACgGpU78P7zn/+UVDDCO2vWLJfpC2FhYWrUqJFmzZpV/T0MQMXn8BJ5AQAAPKXcgTc1NVWS1KNHD3344YeKjY31WKfON8RdAAAAz6nwndZWrVrliX6c30i8AAAAHlPhi9YGDRqkp556qlj7008/rZtvvrlaOhXoWKUBAADAeyoceNesWaMBAwYUa+/fv7/WrFlTLZ063zCFFwAAwHMqHHhPnDjhdvmx0NBQZWZmVkunAAAAgOpS4cDbunVrLVy4sFj7ggUL1LJly2rpVKArOqLLCC8AAIDnVPiitQkTJuimm27S/v371bNnT0nSl19+qXnz5mnRokXV3sHzAXkXAADAcyoceAcOHKglS5boySef1KJFi1SjRg21bdtWK1euVFxcnCf6GPBYhxcAAMBzKjylQZKuvfZaffvtt8rOztb//vc/3XLLLXrooYfUtm3b6u5fQCoab/+bnqV8O6EXAADAEyoVeKWC1RqGDRum5ORkPffcc+rZs6e+++676uxbwNqTYXF5/HzKHl351Eot35Hmox4BAAAErgpNaUhPT9ecOXM0e/ZsZWZm6pZbblFeXp6WLFnCBWvl9PnOQ1r6U/HfM9IzcjX6vc169fYOuuaSJB/0DAAAIDCVe4R34MCBat68ubZt26YXXnhBBw8e1IwZMzzZt4CTbzd6Ytl/3W5zTGiYsnQX0xsAAACqUblHeD/77DPdd999Gj16tJo2berJPgWs9anHlJ6ZJ8nidruRlJaRq/Wpx9S1SW2v9g0AACBQlXuE95tvvlFWVpY6duyoLl266OWXX9aRI0c82beAczgrt1r3AwAAQNnKHXgvu+wyvfHGG0pLS9Of//xnLViwQMnJybLb7UpJSVFWVpYn+xkQEiLDq3U/AAAAlK3CqzTUrFlTI0aM0DfffKPt27frwQcf1PTp05WQkKDrr7/eE30MGJ0bxykxyqqSbjVhkZQUHa7OjVnPGAAAoLpUelkySWrevLmefvpp/fLLL5o/f3519SlgBQdZ9NiAFm63OWb1ThrYUsFB7uf4AgAAoOKqFHgdgoODdeONN+rjjz+ujsMFtH6t6ui6+vZi7YnR4SxJBgAA4AEVvrUwqq5JlOuUhjE9mmh8n+aM7AIAAHhAtYzwomKKzuBtckEtwi4AAICHEHj9gOE+EwAAAB5D4PUD5F0AAADPIfD6QNGAa2eIFwAAwGMIvL5QNN+SdwEAADyGwOsHDIkXAADAY/w+8DZq1EgWi6XY15gxYyRJ3bt3L7bt7rvv9nGvS2fkuiKDnbwLAADgMX6/Du+GDRuUn5/vfLxjxw716dNHN998s7Nt5MiRmjp1qvNxRESEV/tYVUzhBQAA8By/D7wXXHCBy+Pp06erSZMm6tatm7MtIiJCiYmJ5T5mXl6e8vLynI8zMzMlSTabTTabrYo9Lp3NZis2geF0/mmPnxfFOd5z3nvfog7+gTr4B+rgH6iDfyhah6rUw2LMuTO+eOrUKSUnJ2v8+PH629/+JqlgSsPOnTtljFFiYqIGDhyoCRMmlDrKO3nyZE2ZMqVY+7x587wyOrwnw6KZu4Kdj29unK8rE8+ZMgAAAHhdTk6Ohg4dqoyMDEVFRVXouedU4H3//fc1dOhQ/fTTT0pOTpYkvf7662rYsKGSk5O1bds2Pfzww+rcubM+/PDDEo/jboS3fv36OnLkSIXfwIqy2Wx6edEXeqVQ4J18XQvd1qWBR8+L4mw2m1JSUtSnTx+Fhob6ujvnLergH6iDf6AO/oE6+IeidcjMzFR8fHylAq/fT2kobPbs2erfv78z7ErSqFGjnH9v3bq1kpKS1KtXL+3fv19NmjRxexyr1Sqr1VqsPTQ01Dvf2EV+xbAEBfMD5UNeqztKRR38A3XwD9TBP1AH/+CoQ1Vq4ferNDj8+OOP+uKLL/R///d/pe7XpUsXSdK+ffu80a1qcQ4NsgMAAJxzzpnA+9ZbbykhIUHXXnttqftt2bJFkpSUlOSFXlUO950AAADwnnNiSoPdbtdbb72lYcOGKSTkbJf379+vefPmacCAAapdu7a2bdumcePG6eqrr1abNm182OOKYR1eAAAAzzknAu8XX3yhn376SSNGjHBpDwsL0xdffKEXXnhB2dnZql+/vgYNGqTHHnvMRz2tHKY0AAAAeM45EXj79u3rNhTWr19fq1ev9kGPqoZ4CwAA4D3nzBzegFIk8doZ4QUAAPAYAq8PFLtojbwLAADgMQReP0DeBQAA8BwCrw8UDbhMaQAAAPAcAq8fIO8CAAB4DoEXAAAAAY3A6wPFpjRw5wkAAACPIfD6gin1IQAAAKoRgdcPMIcXAADAcwi8PsAqDQAAAN5D4PUDxF0AAADPIfD6QLGAywgvAACAxxB4/QCLNAAAAHgOgdcHig7oGiY1AAAAeAyB1w8wowEAAMBzCLx+gCkNAAAAnkPg9YGi+ZYpDQAAAJ5D4PUH5F0AAACPIfD6ADeeAAAA8B4Crx8g7wIAAHgOgdcXii1LBgAAAE8h8PoAUxoAAAC8h8DrB8i7AAAAnkPg9QOGxAsAAOAxBF4fKL4OLwAAADyFwOsHGOAFAADwHAKvDxQNuFy0BgAA4DkEXj9A3AUAAPAcAq8PFJvDS+IFAADwGAKvH2CVBgAAAM8h8PoB8i4AAIDnEHh9oPiyZCReAAAATyHw+gE7eRcAAMBjCLy+UCTgMqUBAADAcwi8PsCUBgAAAO8h8PoAy5IBAAB4D4HXD7AsGQAAgOcQeH2g6EVq3FoYAADAcwi8Xvb5zkP6+EfXt33FrkNaviPNRz0CAAAIbAReL1q+I033Ltiqk/mu7bk2u0a/t5nQCwAA4AEEXi/JtxtNWbrrzAVrFrf7TFm6S/ksygsAAFCtCLxesj71mNIyckvcbiSlZeRqfeox73UKAADgPEDg9ZLDWSWH3crsBwAAgPIh8HpJQmR4te4HAACA8iHweknnxnFKig4vYfZuwazepOhwdW4c581uAQAABDwCr5cEB1k0aWDLM4/cX5g2aWBLBQeVFIkBAABQGX4deCdPniyLxeLy1aJFC+f23NxcjRkzRrVr11atWrU0aNAgHTp0yIc9Lt01lyRpxuC2qhHs2m4NCdKrt3fQNZck+aZjAAAAAcyvA68ktWrVSmlpac6vb775xrlt3LhxWrp0qT744AOtXr1aBw8e1E033eTD3patX6s66lfP7tJ2RZPahF0AAAAPCfF1B8oSEhKixMTEYu0ZGRmaPXu25s2bp549e0qS3nrrLV188cX67rvvdNlll3m7q+VmKTproVgDAAAAqovfB969e/cqOTlZ4eHh6tq1q6ZNm6YGDRpo06ZNstls6t27t3PfFi1aqEGDBlq7dm2pgTcvL095eXnOx5mZmZIkm80mm83muRdz5hymyBTe/Hy7x8+L4hzvOe+9b1EH/0Ad/AN18A/UwT8UrUNV6uHXgbdLly6aM2eOmjdvrrS0NE2ZMkVXXXWVduzYofT0dIWFhSkmJsblOXXq1FF6enqpx502bZqmTJlSrH3FihWKiIiozpfglimyVsPh3w5r2bJlHj8v3EtJSfF1FyDq4C+og3+gDv6BOvgHRx1ycnIqfQy/Drz9+/d3/r1Nmzbq0qWLGjZsqPfff181atSo9HEfffRRjR8/3vk4MzNT9evXV9++fRUVFVWlPpfFZrPpyzlfSCqYyWCMFB9/gQYM6OjR86I4m82mlJQU9enTR6Ghob7uznmLOvgH6uAfqIN/oA7+oWgdHJ/IV4ZfB96iYmJi1KxZM+3bt099+vTRqVOndPz4cZdR3kOHDrmd81uY1WqV1Wot1h4aGuqVb2zHjIZgi0WnjZEsFn6gfMhbdUfpqIN/oA7+gTr4B+rgHxx1qEot/H6VhsJOnDih/fv3KykpSR07dlRoaKi+/PJL5/bdu3frp59+UteuXX3Yy7I5Am/QmTV3i87pBQAAQPXx6xHehx56SAMHDlTDhg118OBBTZo0ScHBwRoyZIiio6N11113afz48YqLi1NUVJTuvfdede3a1a9XaJDOBtzgM6szmBJuRAEAAICq8+vA+8svv2jIkCE6evSoLrjgAl155ZX67rvvdMEFF0iS/vnPfyooKEiDBg1SXl6e+vXrp1deecXHvS6bc0rDmRFeu73kfQEAAFA1fh14FyxYUOr28PBwzZw5UzNnzvRSj6qHY4TXcRdhRngBAAA855yawxsoHMuShQQXvP3M4QUAAPAcAq8PnB3h5aI1AAAATyPw+oBzlQamNAAAAHgcgdcHHPE2hGXJAAAAPI7A6wPOKQ2OVRpIvAAAAB5D4PWBs1MaLC6PAQAAUP0IvD7AlAYAAADvIfD6QNEpDYbECwAA4DEEXh9w3mmNKQ0AAAAeR+D1AeccXqY0AAAAeByB1wccAffMjdZYpQEAAMCDCLw+UGxKA3kXAADAYwi8vlD0ojUfdgUAACDQEXh9wH7mz7MjvEReAAAATyHw+gAXrQEAAHgPgdcHnBetOZclI/ECAAB4CoHXh4LPjPDaybsAAAAeQ+D1ATt3WgMAAPAaAq8PnF2WzPUxAAAAqh+B14eCuWgNAADA4wi8PnD2TmtMaQAAAPA0Aq8PONfh5aI1AAAAjyPw+oBjQDeIZckAAAA8jsDrQ8zhBQAA8DwCrw+cXaWBwAsAAOBpBF4fMKzDCwAA4DUEXh8oNsLru64AAAAEPAKvDzgCbpBzlQYiLwAAgKcQeH3g7Dq8ro8BAABQ/Qi8PsCUBgAAAO8h8PpA0SkNXLQGAADgOQReH3BOaWBZMgAAAI8j8PqAc0pDEFMaAAAAPI3A6wNF1+FllQYAAADPIfD6AHdaAwAA8B4Crw8Um9JA4gUAAPAYAq8PnF2HlxFeAAAATyPw+oBzWTKL62MAAABUPwKvDxhTkHSDg4LOPCbyAgAAeAqB1wfOzuEt+NNO3gUAAPAYAq8PnJ3S4FiHl8QLAADgKQReH+CiNQAAAO8h8PpA8WXJfNcXAACAQEfg9QHnndaY0gAAAOBxBF4fYIQXAADAewi8PlD01sJ2Ei8AAIDHEHh9wDmlwTHC68O+AAAABDq/DrzTpk3TpZdeqsjISCUkJOjGG2/U7t27Xfbp3r27LBaLy9fdd9/tox5XjGMdXgZ4AQAAPMevA+/q1as1ZswYfffdd0pJSZHNZlPfvn2VnZ3tst/IkSOVlpbm/Hr66ad91OPysZ/503HRmsTd1gAAADwlxNcdKM3y5ctdHs+ZM0cJCQnatGmTrr76amd7RESEEhMTvd29SnNk25CgIJe2QvkXAAAA1cSvA29RGRkZkqS4uDiX9rlz5+q9995TYmKiBg4cqAkTJigiIqLE4+Tl5SkvL8/5ODMzU5Jks9lks9k80POzbDabc86useef7dOpUwoJ9usB94DjqLWna47SUQf/QB38A3XwD9TBPxStQ1XqYTHnyGfpdrtd119/vY4fP65vvvnG2f7666+rYcOGSk5O1rZt2/Twww+rc+fO+vDDD0s81uTJkzVlypRi7fPmzSs1KFeXJ7cE69BJi+5qnq/Zu4MlSc93OS3yLgAAgHs5OTkaOnSoMjIyFBUVVaHnnjOBd/To0frss8/0zTffqF69eiXut3LlSvXq1Uv79u1TkyZN3O7jboS3fv36OnLkSIXfwIqy2Wzq/vRKHc616LXb2+vP730vSXprWAf9nmNTQqRVnRrGOtfohefYbDalpKSoT58+Cg0N9XV3zlvUwT9QB/9AHfwDdfAPReuQmZmp+Pj4SgXec2JKw9ixY/XJJ59ozZo1pYZdSerSpYsklRp4rVarrFZrsfbQ0FCvfGM7LloLCz379t/59mbn35OiwzVpYEtdc0mSx/sC79UdpaMO/oE6+Afq4B+og39w1KEqtfDrD9GNMRo7dqwWL16slStXqnHjxmU+Z8uWLZKkpCQ/DotnxtQ3//i7283pGbka/d5mLd+R5sVOAQAABCa/HuEdM2aM5s2bp48++kiRkZFKT0+XJEVHR6tGjRrav3+/5s2bpwEDBqh27dratm2bxo0bp6uvvlpt2rTxce9L5phDMnfdTyVut0iasnSX+rRMZHoDAABAFfj1CO+rr76qjIwMde/eXUlJSc6vhQsXSpLCwsL0xRdfqG/fvmrRooUefPBBDRo0SEuXLvVxz0vnCLzHsk+Vuk9aRq7Wpx7zSp8AAAAClV+P8JZ1PV39+vW1evVqL/Wm+tgrcJng4axcz3UEAADgPODXI7yQEiLDfd0FAACAcxqB1wccA9e1a4aVuI9FBas1dG4cV+I+AAAAKBuB1wccMxpGXn2h2+2OS9QmDWzJBWsAAABV5NdzeAOVI/BeeVG82+2JrMMLAABQbQi8PuCY0uBu9PZfwzqpR/MESdLa/Ud1OCtXCZEFUxsY7QUAAKg4Aq8POEZ43QXYnFOn9Y9Pd2nJloMuy5Zx9zUAAIDKYQ6vDzhGeN0N2N43f4ve/PZAsTV60zJydfd7m7Vs20Ev9BAAACBwEHh9wDHCa7FYZKngLIWx87/Xsm3cchgAAKC8CLw+4Ai8QRbL2QflZDfSPfM2a/kOQi8AAEB5EHh9wDGlwaKzS5BV1JSlu5RfkVu2AQAAnKcIvD7giKlf7z0ieyWPkZaRq/Wpx6qrSwAAAAGLwOsDjsA74aMdVTrO4azcqncGAAAgwBF4fcBe2WHdIhIiw6vnQAAAAAGMwOsD1THzNq5mqDo2jK2GIwEAAAQ2Aq8PVMcA77Fsmy6b9oUeX7pTa/cf5QI2AACAEnCnNS+rzmB6LNum2d8e0OxvD3AnNgAAgBIwwutlG3/8XZVfjKxk6Rm5Gv0e6/MCAAAUReD1ssOZnllZwTFuzPq8AAAArgi8XhZfy+qxYxuxPi8AAEBRBF4v69AgxuPnYH1eAACAswi8XmaxnJ2/W/0zeQuwPi8AAMBZrNLgZYVn1yZEWXUoM69ajx9XM1Tpmbn6du8RySIdOZGnhMhwdW4cp+AgT0VsAAAA/0Xg9TJjzkbeFeO6adfBTB3OylV8TavWpR7VSyv3Ven4x7JtGrdwS7F2li0DAADnKwKvl9kLBd6QIIu6NqntfBwUZKly4C1Jekau7n5vs8b1bqpG8TUZ9QUAAOcNAq+XFcq7CrK4hk1PXmzmOO0/v9jrbGPUFwAAnA8IvF5WeIncInnX6xebMeoLAADOBwReLys8h7do4O3cOE5J0eFKz8iVN24dwagvAAA4H7AsmZfZS5nSEBxk0aSBLb3cI1fcohgAAAQaAq+XmUJjt0UDryRdc0mSXr29gxKjPHdHttJwi2IAABBoCLxe5jrC636fay5J0reP9NK43s2806ki3N2iON9utHb/UX205Vet3X+UMAwAAM4ZzOH1Mtc5vCVfHBYcZNH9vZuqeWItTVm6S2kZZ1dwsEhemePrWDVi+Y60Yn1gri8AADhXEHi9zJF3S8m6Lq65JEl9WiZqfeoxHc7KVUJkuNrVj9EVT63UsexTpT73vh4XqUmdWjpwJEcvfLGn4PwV6GtCZLiW70jT6Pc2F3ueY67vq7d3KNY/VnoAAAD+hMDrZY4bT7ibv1uS4CI3qJCkJ25opXvmfV/q8z7Y/Iu+ebingoMsbkeKS2KRlBgdro4NY9XtmVVuQ7I5s98jH27X5I93KT2z+kZ/8+2GAA0AAKoNgdfLHFNfqxrfYmuWfVGbYx5u1ya1i40UlzbqayQNvrS+XvpyT6kB2Ug6nmOTZHNpLzz6W9HQy/QJAABQ3Qi8PlKBAV63yntXtsL7FR0pLm3Ut/DavBXlGP2dsnSX+rRMdI7OljVyW57pE4ReAABQUQReL6vMlAZ3yntXttL2Kz7qm12loFtY4ZUeujapXebIbb7daMrSXaVOnygaoAEAAMqDwOtlZwNv1Y5T1l3ZHPNwOzeOK/U4jlHffLvRlU+trFqn3Jj9zf+049cMPbnshxJHbl+5rYNybPllTp8oHKArgjnBAACc3wi8Xuacw1vFEV7HXdlGv7e52DJljiNPGtiy3MFufeqxcl3QVlFf/HBYX/xw2O02R5/HzNus8i7rW96pHA6ljSz3ah5foWNJhGcAAM5FBF5vq+CyZKVx3JWtaKBLrMRFXhUNkolRVuWetisjx1biCHN0RKiaXlBLG378vdRj2U3BiHd5Qm/hG15UdU7wjMFtyz5hIb68oM4XQZtwDwAIFAReL6uuObwO7tbprUwwKe+c4LE9LtIVF8Wrc+M4pexKL3WEefpNrZV32l5m4JWkpwa11vMpe0ucouHw10VbtftQllokRunp5f+t0pzgf3z2X/314jK7Jsm3F9T5ImgT7gn3CDx8n+N8RuD1supalqwwd+v0VlR55wSP69PM+Q9keUaY1+4/Wq7z14utWeoUDSOpZVKUdqVl6rXV/3N7DEf4fGlIe+Xb7eWYE5ynRakWHV/3k2qEhcoaGiRrSLCsIWf+DA2SNSRIIUFBmrBkp08uqPNF0Cbc+9dSeIEQUngNvufv3+flda7XIVCci3Ug8HqZqeYR3upS2TnBZY0wV+TiuuAgS6kBul+rRH2x65DunrvZZWqDg6Pl3vml35CjsG8PBevbT/5b7v3dcVxQ1+2Zlapd06rw0GCFhwarRmiwaoQFn3kcVPD4zLbwsOBCjwu2hYcFKzyk4Dk1QoMVFhykyR97N2j7crWMz3ce0r0Ltp434b48AiGk8Bp8z9+/z8vrXK9DoDhX60Dg9bKK3lrYmyo7J7i0EeaKBumyAnSt8FC3YbfYeS1SfjnmBDeLsqtJ/STZ7EZ5p+3Ks9mVdzq/4O+n7cqz5Ssz16YTefllHuuX33P1y+/Vf+FfSRxB+8qnVqqWNUTBQRYFWSwFfwZZFGyRS5vLdotFwUFFtlssOpKdV67VMh5Y+L3qxUbIooJf3iyWggsxCz8OcrRZJIssZx6f/WXPsZ+x27UzzaKU70sO91LBXf3ybHaFBAc5j13Q/7PHcrwex3kc2y2WM6+z0DYjo8eW7PDbpfACIaTwGnwvUJZ8PNfrUNi5ODrqcC7XgcDrZdU9h7e6Vdec4KLHrEiQLi1Al/fiumf/2FZPr9hdxsiyVaMvztZ117ZVaGhoicdau/+ohrzxXZnnfGzAxWp8QU2dtOUr12Yv+PNUvnJt+Tp55ivXse1U4ceFt9uVe2bb6XIuXeGJ1TXKsnRrWjUfMVjS6VL3OJ5j0/0Lt1TzeUvmCPdN/76sYMQ9JEhhZ6a7hIUEKSw4SNbQgj8L2kve5vI8576Fn+u6LTjIogkflT66P3npLl3d7AK3/5Y4frE2Z45gjOvzC9qMy2PbKZtyTksZJ20Ktbl/ftHnura5Pic/32hiWa/h453q2iReYcFBCgqS85eSID/5z99TYTHfbmTLt+tUvl2nTttly7fLdtroVL5dObmn9OMJadOPv8uuIJ3Kt8uWf2b/0/Yzjws9L9+cbT/TdupMmy3frvSMk+X6Jfa2N75TYnS4Qs98T57906Kw4GCFhlic36+hwY4vi6yFHjufFxzk3N/d8UKDgipU40AJ7dK5OzoqSXm2fE3y8qeO1YnA62W2M8OOp07na+3+o375m111zAkuytsX1yXG1ChzZPnv/Vso/8dNZR6rvNMy7ryycbXW8pu9v+n22evL3G/CdRfr4sQo2Y2Ub4zsdqN8uzn7d1Pw2G6M8u1y03b276m/ZWv+hp/LPOeASxKVFFNDdmNkTEEAMpLzcUFWN7LbCwKQ3aiE/Yzsdrt2pqbpx+ygMs/bNKGW4mqGFTzP8XxT8Jocfzfm7OsyhfYpeHz2eSdPnVZWOUbu7UbKPpWv7FNl7+stRgUjKi0nfl7NRw7RoxtWVfMx3TOS0jPz1HbKCrfbg4p8QhFksbi0BZ35VCLIooK/u9un0CceQRY5w7Tbfc586lH42EdPlO8Tj5te+VYRYSFnAmjBp0O2UoJq2b/LhkjbN1T2ra2U71KPefV8ocGWQsE56ExwtriEZ0dwzs47Xa46/G3xdl10Qa2C44ScDeXOYxYK6iGFgnnhbSFn9rfY85VvXH+5q6rqHB01puBTSccgSq4tX7mnC/39THve6XzXfWz2M/ud2V7C83JP5zs/8XRsK2sQpirr5XsDgdeLlu9I098X75AkZeXla8gb350zv9lVB29eXFeeOcG9msdr2Y/l63d1rnlcXl2bxJfrtQ6/vPqCdr7d6Ks9v5V5zhlDO1TbOW02m16cf1Av7yp736k3XFJt/5CWd+R+5tAOuqRulE45prmcPhtgCtryCx4X2ZZny1eeI+wU3uZ4jpttjuNn59l00mavltfpTY7BZovO/IJThWPZjWTPN6raUbxj6y8ZVXp+mCOYhQQpNMiifFueomrVdI76Fw5sYYWCoTMUuoTEMwHvzLZffs/Rm98eKLMPd17RSHVjapwZKXYzAu38uyneXmhEufDoc+H9i05FK/hlIF9S9f0iubAcv6xXTIgeXJdS8H4GnQ3RYYWCdEFwLhTez9QgJCjozP4FI93BQRYt/v7XUqdtjVu4VZ9sS3MGWce0uqIh1bHNX1V0mVNvCZjAO3PmTD3zzDNKT09X27ZtNWPGDHXu3NnX3XI6l+e9+JPqnBNss9nKfd7qXPO4vHwRtH0V7ptEGSVGWXUoM69Kdw6siPL+8nTNJd7/eK68YfzNYZ10aeM4lxvZOP52NnxaXB4XVngfm82mz5cv1zX9r1FYoSk+jmMXO24Z07LK+xreHnGpOjWMOzPaf+ZTiiKfTpgzI/T5pmCUPt8u56cSjtF8x8h9vt2cGfE/+4nH2X3Ojvaffe7ZTz2c5z3z/P8dOaH3vvupzNcwulsTXZwcdSaQuhmldJke4Poxf2iwxeW9tNlsWrZsmQYMuLLUqVbllW83+mxHepnf549dW/0/10X7UTQ4u46GFx4dN7IVGhX/4WCmZq1xvzpPYd2bxSuuptX5vMIj7KftRkWngrj+eba9MGNU8IupJHn4U56Ttnx9sq3i08VCgizOi6OtIQV/Oi6eDg8NUnhIwd+tRbe77FvwZ2nP3/7rcY18p+xPRcv7Say3BUTgXbhwocaPH69Zs2apS5cueuGFF9SvXz/t3r1bCQkJvu5eQM0/8gfVOSe4ouet7vnN5Tmnt4O2L84ZZJEeG9BC9y7YGvDhvjzKG8a7NU+otv5ZTJCCg+Qctaqq8r6GKy+6wG//3cu3G335w+EyX8ND/Zr77Wvwl+/zggtnC4JTRV3XJlkfbT1YZh1mD+9c5ddhjJEt3+hkXp6WLV+h7j17yViC3YbjU2dGwk/bz/698Kj36UIhevsvGVq+M73M8/+hfV11ahTrDJmFA6m1WFuwwkOq5+e1PC6IrFPuT1j9UUAE3ueff14jR47UnXfeKUmaNWuWPv30U7355pt65JFHfNy7sm/b6+/zXvyRL8Kn5Jn5zWXxVdD29jn7tapzXoT78vCXkFIVvAb/4a/f5+XlzTpYLBaFhVhkMSGKCJHia1mrZaR97f6j5Qq8t3Sq77c54Fz/eTjnA++pU6e0adMmPfroo862oKAg9e7dW2vXrnX7nLy8POXl5TkfZ2ZmSir4KKkiH3OXV9rx7HLvZ7NFVfv5A1mnBlGSCt4ze/5p2SvwiZOj1p6ouSdU5bX68zkL16FX83h1b3qVNv74uw5n5Skh0qpODWMrPAWlInxxzvL2a8bgtnpi2X+Vnnn236vEaKv+3r+FejWPr9b+eeLnwduvwRMCoQ6S/36fl9e5Xof29SLLMW3Lqvb1Iv26Hr6uQ1WObTHVeQmiDxw8eFB169bVf/7zH3Xt2tXZ/te//lWrV6/WunXrij1n8uTJmjJlSrH2efPmKSIiotr7uDfDopd3lf0xztiW+WoafU6XA0A1sxtpf6ZFmTYpKrRgvrOfDqCUiNeA6nIu12HrUYve3OOYflC40wX/749oZlfb2udGBvBVHXJycjR06FBlZGQoKqpiA4Tn/AhvZTz66KMaP36883FmZqbq16+vvn37VvgNLI98u9Gi59aU+Zvd2Fuv9tuPAgKRzWZTSkqK+vTpUy0fWaFyqIN/oA7+gTr4B0/UYYCkDjsPFRsdTYoO19/7t1C/VnWq5TyBpGgdHJ/IV8Y5H3jj4+MVHBysQ4cOubQfOnRIiYmJbp9jtVpltVqLtYeGhnrkH5hQSZOvb1XGvJdWCreGVfu5UTZP1R0VQx38A3XwD9TBP1R3Ha5rV0/929Q9Z++05iuOOlSlFt65tM+DwsLC1LFjR3355ZfONrvdri+//NJlioOvOS4aqBPlGrQTo8NZkgwAgPOE4+LnG9rVVdcmtQm7XnLOj/BK0vjx4zVs2DB16tRJnTt31gsvvKDs7Gznqg3+4ppLktS9aW29vHC5LmzVTkkxNfnNDgAAwMMCIvDeeuut+u233zRx4kSlp6erXbt2Wr58uerU8b/5MMFBFjWNNhrQJomPqwAAALwgIAKvJI0dO1Zjx471dTcAAADgZ875ObwAAABAaQi8AAAACGgEXgAAAAQ0Ai8AAAACGoEXAAAAAY3ACwAAgIBG4AUAAEBAI/ACAAAgoBF4AQAAENAIvAAAAAhoBF4AAAAENAIvAAAAAlqIrzvgD4wxkqTMzEyPn8tmsyknJ0eZmZkKDQ31+PlQMmrhH6iDf6AO/oE6+Afq4B+K1sGR0xy5rSIIvJKysrIkSfXr1/dxTwAAAFCarKwsRUdHV+g5FlOZmBxg7Ha7Dh48qMjISFksFo+eKzMzU/Xr19fPP/+sqKgoj54LpaMW/oE6+Afq4B+og3+gDv6haB2MMcrKylJycrKCgio2K5cRXklBQUGqV6+eV88ZFRXFD5GfoBb+gTr4B+rgH6iDf6AO/qFwHSo6suvARWsAAAAIaAReAAAABDQCr5dZrVZNmjRJVqvV110571EL/0Ad/AN18A/UwT9QB/9QnXXgojUAAAAENEZ4AQAAENAIvAAAAAhoBF4AAAAENAIvAAAAAhqB18tmzpypRo0aKTw8XF26dNH69et93aWAtmbNGg0cOFDJycmyWCxasmSJy3ZjjCZOnKikpCTVqFFDvXv31t69e33T2QA2bdo0XXrppYqMjFRCQoJuvPFG7d6922Wf3NxcjRkzRrVr11atWrU0aNAgHTp0yEc9Dkyvvvqq2rRp41zEvWvXrvrss8+c26mBb0yfPl0Wi0UPPPCAs41aeN7kyZNlsVhcvlq0aOHcTg2859dff9Xtt9+u2rVrq0aNGmrdurU2btzo3F4d/1cTeL1o4cKFGj9+vCZNmqTNmzerbdu26tevnw4fPuzrrgWs7OxstW3bVjNnznS7/emnn9ZLL72kWbNmad26dapZs6b69eun3NxcL/c0sK1evVpjxozRd999p5SUFNlsNvXt21fZ2dnOfcaNG6elS5fqgw8+0OrVq3Xw4EHddNNNPux14KlXr56mT5+uTZs2aePGjerZs6duuOEG7dy5UxI18IUNGzbotddeU5s2bVzaqYV3tGrVSmlpac6vb775xrmNGnjH77//riuuuEKhoaH67LPPtGvXLj333HOKjY117lMt/1cbeE3nzp3NmDFjnI/z8/NNcnKymTZtmg97df6QZBYvXux8bLfbTWJionnmmWecbcePHzdWq9XMnz/fBz08fxw+fNhIMqtXrzbGFLzvoaGh5oMPPnDu88MPPxhJZu3atb7q5nkhNjbW/Otf/6IGPpCVlWWaNm1qUlJSTLdu3cz9999vjOHnwVsmTZpk2rZt63YbNfCehx9+2Fx55ZUlbq+u/6sZ4fWSU6dOadOmTerdu7ezLSgoSL1799batWt92LPzV2pqqtLT011qEh0drS5dulATD8vIyJAkxcXFSZI2bdokm83mUosWLVqoQYMG1MJD8vPztWDBAmVnZ6tr167UwAfGjBmja6+91uU9l/h58Ka9e/cqOTlZF154oW677Tb99NNPkqiBN3388cfq1KmTbr75ZiUkJKh9+/Z64403nNur6/9qAq+XHDlyRPn5+apTp45Le506dZSenu6jXp3fHO87NfEuu92uBx54QFdccYUuueQSSQW1CAsLU0xMjMu+1KL6bd++XbVq1ZLVatXdd9+txYsXq2XLltTAyxYsWKDNmzdr2rRpxbZRC+/o0qWL5syZo+XLl+vVV19VamqqrrrqKmVlZVEDL/rf//6nV199VU2bNtXnn3+u0aNH67777tPbb78tqfr+rw6pvi4DQNnGjBmjHTt2uMyVg/c0b95cW7ZsUUZGhhYtWqRhw4Zp9erVvu7WeeXnn3/W/fffr5SUFIWHh/u6O+et/v37O//epk0bdenSRQ0bNtT777+vGjVq+LBn5xe73a5OnTrpySeflCS1b99eO3bs0KxZszRs2LBqOw8jvF4SHx+v4ODgYld4Hjp0SImJiT7q1fnN8b5TE+8ZO3asPvnkE61atUr16tVzticmJurUqVM6fvy4y/7UovqFhYXpoosuUseOHTVt2jS1bdtWL774IjXwok2bNunw4cPq0KGDQkJCFBISotWrV+ull15SSEiI6tSpQy18ICYmRs2aNdO+ffv4efCipKQktWzZ0qXt4osvdk4vqa7/qwm8XhIWFqaOHTvqyy+/dLbZ7XZ9+eWX6tq1qw97dv5q3LixEhMTXWqSmZmpdevWUZNqZozR2LFjtXjxYq1cuVKNGzd22d6xY0eFhoa61GL37t366aefqIWH2e125eXlUQMv6tWrl7Zv364tW7Y4vzp16qTbbrvN+Xdq4X0nTpzQ/v37lZSUxM+DF11xxRXFlqncs2ePGjZsKKka/6+uypV1qJgFCxYYq9Vq5syZY3bt2mVGjRplYmJiTHp6uq+7FrCysrLM999/b77//nsjyTz//PPm+++/Nz/++KMxxpjp06ebmJgY89FHH5lt27aZG264wTRu3NicPHnSxz0PLKNHjzbR0dHmq6++Mmlpac6vnJwc5z533323adCggVm5cqXZuHGj6dq1q+natasPex14HnnkEbN69WqTmppqtm3bZh555BFjsVjMihUrjDHUwJcKr9JgDLXwhgcffNB89dVXJjU11Xz77bemd+/eJj4+3hw+fNgYQw28Zf369SYkJMT84x//MHv37jVz5841ERER5r333nPuUx3/VxN4vWzGjBmmQYMGJiwszHTu3Nl89913vu5SQFu1apWRVOxr2LBhxpiC5U4mTJhg6tSpY6xWq+nVq5fZvXu3bzsdgNzVQJJ56623nPucPHnS3HPPPSY2NtZERESYP/zhDyYtLc13nQ5AI0aMMA0bNjRhYWHmggsuML169XKGXWOogS8VDbzUwvNuvfVWk5SUZMLCwkzdunXNrbfeavbt2+fcTg28Z+nSpeaSSy4xVqvVtGjRwrz++usu26vj/2qLMcZUehwaAAAA8HPM4QUAAEBAI/ACAAAgoBF4AQAAENAIvAAAAAhoBF4AAAAENAIvAAAAAhqBFwAAAAGNwAsAAICARuAFAA8bPny4brzxRq+c6+jRo0pISNCBAweqdJzu3bvrgQceKPf+R44cUUJCgn755ReX9sGDB+u5556rUl8AoKq40xoAVIHFYil1+6RJkzRu3DgZYxQTE+Px/owfP15ZWVl64403qnScY8eOKTQ0VJGRkeV+zkMPPaTff/9ds2fPdrbt2LFDV199tVJTUxUdHV2lPgFAZRF4AaAK0tPTnX9fuHChJk6cqN27dzvbatWqpVq1anmlLzk5OUpKStLnn3+uyy67zCvnLGznzp3q2LGjDh48qLi4OGf7pZdequHDh2vMmDFe7xMASExpAIAqSUxMdH5FR0fLYrG4tNWqVavYlIbu3bvr3nvv1QMPPKDY2FjVqVNHb7zxhrKzs3XnnXcqMjJSF110kT777DOXc+3YsUP9+/dXrVq1VKdOHf3pT3/SkSNHnNuXLVsmq9XqEna/+uorWSwWff7552rfvr1q1Kihnj176vDhw/rss8908cUXKyoqSkOHDlVOTo5LHwtPaWjUqJGefPJJjRgxQpGRkWrQoIFef/11l/61atVKycnJWrx4sUv7wIEDtWDBgqq8zQBQJQReAPCBt99+W/Hx8Vq/fr3uvfdejR49WjfffLMuv/xybd68WX379tWf/vQnZwg9fvy4evbsqfbt22vjxo1avny5Dh06pFtuucV5zK+//lodO3Z0e77Jkyfr5Zdf1n/+8x/9/PPPuuWWW/TCCy9o3rx5+vTTT7VixQrNmDGj1D4/99xz6tSpk77//nvdc889Gj16tMtotiR17txZX3/9dbG29evXKy8vrzJvFQBUGYEXAHygbdu2euyxx9S0aVM9+uijCg8PV3x8vEaOHKmmTZtq4sSJOnr0qLZt2yZJevnll9W+fXs9+eSTatGihdq3b68333xTq1at0p49eyRJP/74o5KTk92e74knntAVV1yh9u3b66677tLq1av16quvqn379rrqqqv0xz/+UatWrSq1zwMGDNA999yjiy66SA8//LDi4+OLPSc5OVk//vhjsbZTp065TP8AAG8i8AKAD7Rp08b59+DgYNWuXVutW7d2ttWpU0eSdPjwYUnS1q1btWrVKuec4Fq1aqlFixaSpP3790uSTp48qfDw8DLPV6dOHUVEROjCCy90aXOcqzx9dkzdKPqcGjVquEyNcLRJKtYOAN4S4usOAMD5KDQ01OWxxWJxaXOs/mC32yVJJ06c0MCBA/XUU08VO1ZSUpIkKT4+Xr///nuZ5yt6Lkeb41wV6XPR5xw7dkwXXHBBsTZJxdoBwFsIvABwDujQoYP+/e9/q1GjRgoJcf9Pd/v27fXee+95uWeuduzYoe7duxdrq1evnuLj433TKQDnPaY0AMA5YMyYMTp27JiGDBmiDRs2aP/+/fr888915513Kj8/X5LUr18/7dy5s8RRXk/LycnRpk2b1LdvX5f2r7/+ulgbAHgTgRcAzgHJycn69ttvlZ+fr759+6p169Z64IEHFBMTo6Cggn/KW7durQ4dOuj999/3SR8/+ugjNWjQQFdddZWzLTc3V0uWLNHIkSN90icAkLjxBAAElE8//VR/+ctftGPHDmcQ9pbLLrtM9913n4YOHepse/XVV7V48WKtWLHCq30BgMKYwwsAAeTaa6/V3r179euvv6p+/fpeO++RI0d00003aciQIS7toaGhZa7vCwCexggvAAAAAhpzeAEAABDQCLwAAAAIaAReAAAABDQCLwAAAAIagRcAAAABjcALAACAgEbgBQAAQEAj8AIAACCgEXgBAAAQ0P4fQPauFN25bUoAAAAASUVORK5CYII=",
      "text/plain": [
       "<Figure size 800x600 with 1 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "# 示例2：绘制某个体素的时间活度曲线(time activity curve, TAC)\n",
    "x, y, z = 84, 84, 37  # 选择中心点 (n1/2, n2/2, n3/2)\n",
    "# 注意：Python是从0开始计数，所以如果要取像素点（84,84,37），矩阵位置对应为（83,83,36）\n",
    "tac = data[x-1, y-1, z-1, :]  # 提取该体素在所有时间点的数据\n",
    "plt.figure(figsize=(8, 6))\n",
    "plt.plot(td.values, tac, marker='o') #  CT\n",
    "plt.title(\"Central Voxel TAC\")\n",
    "plt.xlabel(\"Time(min))\")\n",
    "plt.ylabel(\"Activity\")\n",
    "plt.grid(True)\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 14,
   "id": "d8f793ff",
   "metadata": {},
   "outputs": [],
   "source": [
    "cp = GT['tac4'] # arterial input function. "
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 19,
   "id": "be8c2f7e",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 800x600 with 1 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "plt.figure(figsize=(8, 6))\n",
    "plt.plot(td.values, tac, marker='o')\n",
    "plt.xlabel(\"Time(min)\")\n",
    "plt.ylabel(\"Activity\")\n",
    "plt.grid(True)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "e9e0b4f4",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "已保存PDF到: Voxel_TAC_Fitting_Results.pdf\n"
     ]
    }
   ],
   "source": [
    "from scipy.optimize import curve_fit\n",
    "from matplotlib.backends.backend_pdf import PdfPages\n",
    "from matplotlib.backends.backend_pdf import PdfPages\n",
    "\n",
    "def two_tissue_model(t, K1, k2, k3, k4, cp, ts, te):\n",
    "\n",
    "    dt = te - ts\n",
    "    n = len(t)\n",
    "    Ct = np.zeros(n)\n",
    "    for i in range(n):\n",
    "        integral = 0\n",
    "        for j in range(i+1):\n",
    "            tau = t[j]\n",
    "            exp1 = np.exp(-(k2 + k3 + k4) * (t[i] - tau))\n",
    "            exp2 = np.exp(-k4 * (t[i] - tau))\n",
    "            h = K1 * ( (k3 + k4 - k2) / (k3 + k4 - k2 - k4) * exp1 + (k2 - k3 - k4) / (k2 - k3 - k4 - k4) * exp2 )\n",
    "            integral += cp[j] * h * dt[j]\n",
    "        Ct[i] = integral\n",
    "    return Ct\n",
    "\n",
    "def fit_voxel_tac(td, tac, cp, ts, te):\n",
    "    p0 = [0.1, 0.1, 0.01, 0.01]\n",
    "    bounds = ([0, 0, 0, 0], [10, 10, 1, 1])\n",
    "    def model_func(t, K1, k2, k3, k4):\n",
    "        return two_tissue_model(t, K1, k2, k3, k4, cp, ts, te)\n",
    "    try:\n",
    "        popt, _ = curve_fit(model_func, td, tac, p0=p0, bounds=bounds, maxfev=2000)\n",
    "        fit_curve = model_func(td, *popt)\n",
    "        return popt, fit_curve\n",
    "    except Exception:\n",
    "        return None, np.full_like(td, np.nan)\n",
    "\n",
    "valid_mask = (np.any(data != 0, axis=3)) & (~np.any(np.isnan(data), axis=3))\n",
    "valid_indices = np.argwhere(valid_mask)\n",
    "np.random.seed(42)\n",
    "if len(valid_indices) < 100:\n",
    "    raise ValueError(\"有效体素数量不足100，请检查数据！\")\n",
    "coords = valid_indices[np.random.choice(len(valid_indices), 100, replace=False)]\n",
    "\n",
    "\n",
    "pdf_path = \"Results.pdf\"\n",
    "plots_per_page = 9\n",
    "with PdfPages(pdf_path) as pdf:\n",
    "    page_count = 0\n",
    "    for page_start in range(0, len(coords), plots_per_page):\n",
    "        fig, axes = plt.subplots(3, 3, figsize=(15, 12))\n",
    "        axes = axes.flatten()\n",
    "        for i in range(plots_per_page):\n",
    "            idx = page_start + i\n",
    "            if idx >= len(coords):\n",
    "                axes[i].axis('off')\n",
    "                continue\n",
    "            xi, yi, zi = coords[idx]\n",
    "            tac = data[xi, yi, zi, :]\n",
    "            if np.all(tac == 0) or np.any(np.isnan(tac)):\n",
    "                axes[i].axis('off')\n",
    "                continue\n",
    "            popt, fit_curve = fit_voxel_tac(td.values, tac, cp.values, ts.values, te.values)\n",
    "            axes[i].scatter(td.values, tac, color='gray', label='Data', s=15)\n",
    "            if popt is not None:\n",
    "                axes[i].plot(td.values, fit_curve, color='crimson', label='SA Fit', linewidth=2)\n",
    "                axes[i].set_title(f'Voxel ({xi+1},{yi+1},{zi+1})\\nK1={popt[0]:.3f}, k2={popt[1]:.3f}, k3={popt[2]:.3f}, k4={popt[3]:.3f}')\n",
    "            else:\n",
    "                axes[i].set_title(f'Voxel ({xi+1},{yi+1},{zi+1})\\n拟合失败')\n",
    "            axes[i].set_xlabel('Time (min)')\n",
    "            axes[i].set_ylabel('Activity (kBq/cc)')\n",
    "            axes[i].legend(fontsize=8)\n",
    "        plt.tight_layout()\n",
    "        pdf.savefig(fig)\n",
    "        plt.close(fig)\n",
    "print(f\"已保存PDF到: {pdf_path}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 谱分析(Spectral Analysis)实现\n",
    "import numpy as np\n",
    "from scipy.optimize import nnls\n",
    "from scipy.integrate import cumtrapz\n",
    "\n",
    "def spectral_analysis(td, tac, cp, ts, te, alpha_range=None):\n",
    "    \"\"\"\n",
    "    谱分析方法拟合TAC曲线\n",
    "    \n",
    "    参数:\n",
    "    td: 时间中点\n",
    "    tac: 组织时间活度曲线\n",
    "    cp: 血浆输入函数\n",
    "    ts, te: 每帧起止时间\n",
    "    alpha_range: 指数衰减常数范围，默认为对数均匀分布\n",
    "    \n",
    "    返回:\n",
    "    fit_curve: 拟合曲线\n",
    "    spectrum: 频谱\n",
    "    alpha_values: 对应的alpha值\n",
    "    K1: 血流参数\n",
    "    \"\"\"\n",
    "    \n",
    "    if alpha_range is None:\n",
    "        # 默认alpha范围：从0.001到10，对数均匀分布\n",
    "        alpha_values = np.logspace(-3, 1, 100)\n",
    "    else:\n",
    "        alpha_values = alpha_range\n",
    "    \n",
    "    n_time = len(td)\n",
    "    n_alpha = len(alpha_values)\n",
    "    \n",
    "    # 构建基函数矩阵\n",
    "    # 每一列对应一个alpha值的基函数\n",
    "    basis_matrix = np.zeros((n_time, n_alpha + 1))  # +1 for vascular component\n",
    "    \n",
    "    # 计算每个alpha对应的基函数\n",
    "    for i, alpha in enumerate(alpha_values):\n",
    "        # 对于每个时间点，计算卷积积分\n",
    "        for j in range(n_time):\n",
    "            integral = 0\n",
    "            for k in range(j + 1):\n",
    "                # 指数衰减的冲激响应函数\n",
    "                dt = te.iloc[k] - ts.iloc[k]\n",
    "                tau = td.iloc[k]\n",
    "                t_current = td.iloc[j]\n",
    "                \n",
    "                # 计算从tau到t_current的指数衰减\n",
    "                if t_current >= tau:\n",
    "                    exp_decay = np.exp(-alpha * (t_current - tau))\n",
    "                    integral += cp.iloc[k] * exp_decay * dt\n",
    "            \n",
    "            basis_matrix[j, i] = integral\n",
    "    \n",
    "    # 添加血管成分（无衰减）\n",
    "    basis_matrix[:, -1] = cp.values\n",
    "    \n",
    "    # 使用非负最小二乘法求解\n",
    "    try:\n",
    "        spectrum_coeffs, residual = nnls(basis_matrix, tac)\n",
    "        \n",
    "        # 计算拟合曲线\n",
    "        fit_curve = basis_matrix @ spectrum_coeffs\n",
    "        \n",
    "        # 提取频谱和血管成分\n",
    "        spectrum = spectrum_coeffs[:-1]\n",
    "        vascular_component = spectrum_coeffs[-1]\n",
    "        \n",
    "        # 计算等效K1（总流入速率常数）\n",
    "        K1_equiv = np.sum(spectrum)\n",
    "        \n",
    "        return fit_curve, spectrum, alpha_values, K1_equiv, vascular_component, residual\n",
    "    \n",
    "    except Exception as e:\n",
    "        print(f\"谱分析拟合失败: {e}\")\n",
    "        return np.full_like(td, np.nan), None, alpha_values, None, None, None"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 为所有像素点进行谱分析和房室模型拟合，并生成比较PDF\n",
    "import matplotlib.pyplot as plt\n",
    "from matplotlib.backends.backend_pdf import PdfPages\n",
    "import time\n",
    "\n",
    "def compare_fitting_methods(coords, data, td, cp, ts, te, pdf_path=\"TAC_Comparison_Results.pdf\"):\n",
    "    \"\"\"\n",
    "    比较房室模型和谱分析两种拟合方法\n",
    "    \"\"\"\n",
    "    \n",
    "    plots_per_page = 6  # 每页6个图，便于比较\n",
    "    \n",
    "    with PdfPages(pdf_path) as pdf:\n",
    "        page_count = 0\n",
    "        \n",
    "        for page_start in range(0, len(coords), plots_per_page):\n",
    "            fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n",
    "            axes = axes.flatten()\n",
    "            \n",
    "            for i in range(plots_per_page):\n",
    "                idx = page_start + i\n",
    "                if idx >= len(coords):\n",
    "                    axes[i].axis('off')\n",
    "                    continue\n",
    "                \n",
    "                xi, yi, zi = coords[idx]\n",
    "                tac = data[xi, yi, zi, :]\n",
    "                \n",
    "                # 跳过无效数据\n",
    "                if np.all(tac == 0) or np.any(np.isnan(tac)):\n",
    "                    axes[i].axis('off')\n",
    "                    continue\n",
    "                \n",
    "                # 房室模型拟合\n",
    "                popt_compartment, fit_curve_compartment = fit_voxel_tac(\n",
    "                    td.values, tac, cp.values, ts.values, te.values\n",
    "                )\n",
    "                \n",
    "                # 谱分析拟合\n",
    "                fit_curve_spectral, spectrum, alpha_values, K1_equiv, vascular_comp, residual = spectral_analysis(\n",
    "                    td, tac, cp, ts, te\n",
    "                )\n",
    "                \n",
    "                # 绘制比较图\n",
    "                axes[i].scatter(td.values, tac, color='black', label='Original Data', s=25, alpha=0.7)\n",
    "                \n",
    "                # 房室模型拟合线\n",
    "                if popt_compartment is not None:\n",
    "                    axes[i].plot(td.values, fit_curve_compartment, color='red', \n",
    "                               label='Compartment Model', linewidth=2, linestyle='-')\n",
    "                    compartment_info = f'K1={popt_compartment[0]:.3f}, k2={popt_compartment[1]:.3f}'\n",
    "                else:\n",
    "                    compartment_info = 'Compartment: Failed'\n",
    "                \n",
    "                # 谱分析拟合线\n",
    "                if not np.any(np.isnan(fit_curve_spectral)):\n",
    "                    axes[i].plot(td.values, fit_curve_spectral, color='blue', \n",
    "                               label='Spectral Analysis', linewidth=2, linestyle='--')\n",
    "                    spectral_info = f'K1_eq={K1_equiv:.3f}, Vasc={vascular_comp:.3f}'\n",
    "                else:\n",
    "                    spectral_info = 'Spectral: Failed'\n",
    "                \n",
    "                # 设置标题和标签\n",
    "                title = f'Voxel ({xi+1},{yi+1},{zi+1})\\n{compartment_info}\\n{spectral_info}'\n",
    "                axes[i].set_title(title, fontsize=10)\n",
    "                axes[i].set_xlabel('Time (min)', fontsize=9)\n",
    "                axes[i].set_ylabel('Activity (kBq/cc)', fontsize=9)\n",
    "                axes[i].legend(fontsize=8)\n",
    "                axes[i].grid(True, alpha=0.3)\n",
    "                \n",
    "                # 调整坐标轴范围\n",
    "                axes[i].set_xlim(0, max(td.values))\n",
    "                if np.max(tac) > 0:\n",
    "                    axes[i].set_ylim(0, np.max(tac) * 1.1)\n",
    "            \n",
    "            plt.tight_layout()\n",
    "            plt.suptitle(f'TAC Fitting Comparison - Page {page_count + 1}', \n",
    "                        fontsize=14, y=0.98)\n",
    "            pdf.savefig(fig, dpi=150)\n",
    "            plt.close(fig)\n",
    "            page_count += 1\n",
    "            \n",
    "            # 显示进度\n",
    "            if page_count % 5 == 0:\n",
    "                print(f\"已处理 {min(page_start + plots_per_page, len(coords))} / {len(coords)} 个体素\")\n",
    "    \n",
    "    print(f\"\\n比较结果已保存到: {pdf_path}\")\n",
    "    print(f\"总共处理了 {len(coords)} 个体素，生成了 {page_count} 页\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 执行谱分析和房室模型比较\n",
    "print(\"开始进行谱分析和房室模型比较...\")\n",
    "print(\"这可能需要一些时间，请耐心等待...\")\n",
    "\n",
    "# 使用之前选择的100个有效体素坐标\n",
    "compare_fitting_methods(coords, data, td, cp, ts, te, \"Spectral_vs_Compartment_Comparison.pdf\")\n",
    "\n",
    "print(\"\\n分析完成！\")\n",
    "print(\"PDF文件包含了100个随机选择的体素的原始数据、房室模型拟合线和谱分析拟合线的比较。\")\n",
    "print(\"每页显示6个体素的比较结果，便于查看和分析。\")"
   ]
  },
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
