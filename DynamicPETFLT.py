#!/usr/bin/env python
# coding: utf-8

# In[1]:


import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
# 前提：已生成data数组
n1 = 168
n2 = 168
n3 = 74
nt = 45
nn = n1 * n2 * n3

# 读取数据（与之前代码一致）
file_path = r"C:\Users\<USER>\Desktop\010-001-1.dyn.floatImage"
with open(file_path, "rb") as f:
    data = np.fromfile(f, dtype=">f4", count=nn * nt)
data = data.reshape(n1, n2, n3, nt, order="F") # 注意Python里面矩阵排列是按行排，所以这里要设置order 


# In[2]:


# 定义并注册NIH调色盘 (与Amide中一致)
from matplotlib.colors import LinearSegmentedColormap
nih_colors = [
    (0.0, 0.0, 0.0),    # 黑色
    (0.0, 0.0, 1.0),    # 蓝色
    (0.0, 1.0, 1.0),    # 青色
    (0.486, 0.988, 0.0),# 草绿色
    (1.0, 1.0, 0.0),    # 黄色
    (1.0, 0.0, 0.0)     # 红色
]
nih_cmap = LinearSegmentedColormap.from_list("nih", nih_colors)
plt.register_cmap(cmap=nih_cmap)


# In[3]:


# 示例1：绘制某一时间点的2D切片（沿n3轴的中间层）
time_point = 44  # 选择最后一个时间点
slice_idx = n3 // 2  # 选择n3维度的中间层
# 注意：Python是从0开始计数，所以这里两个位置都要 减一
slice_data = data[:, :, slice_idx-1, time_point-1]

plt.figure(figsize=(8, 6))
plt.imshow(slice_data, cmap="nih")
plt.title(f"2D Slice at Time {time_point}, z={slice_idx}")
plt.colorbar(label="Intensity")
plt.show()


# In[4]:


# read start time(ts) and end time(te) for each time frame. 
# and also the four time activity curves(tacs) you need to search - which is also ground truth
GT = pd.read_csv('C:/Users/<USER>/Desktop/groundtruth.csv')


# In[5]:


ts = GT['ts']  # Extract the 'ts' column
te = GT['te']  # Extract the 'te' column
td = (ts + te) / 2 # mid time point of each time frame


# In[6]:


# 示例2：绘制某个体素的时间活度曲线(time activity curve, TAC)
x, y, z = 84, 84, 37  # 选择中心点 (n1/2, n2/2, n3/2)
# 注意：Python是从0开始计数，所以如果要取像素点（84,84,37），矩阵位置对应为（83,83,36）
tac = data[x-1, y-1, z-1, :]  # 提取该体素在所有时间点的数据
plt.figure(figsize=(8, 6))
plt.plot(td, tac, marker='o')  #  CT
plt.title(f"Time-Activity Curve at voxel ({x}, {y}, {z})")
plt.xlabel("Time(min))")
plt.ylabel("Activity")
plt.grid(True)
plt.show()


# In[7]:


cp = GT['tac4'] # arterial input function. 


# In[8]:


plt.figure(figsize=(8, 6))
plt.plot(td, cp, marker='o')
plt.xlabel("Time(min)")
plt.ylabel("Activity")
plt.grid(True)


# In[9]:

from scipy.optimize import curve_fit
from matplotlib.backends.backend_pdf import PdfPages

# --- 两房室模型函数 ---
def two_tissue_model(t, K1, k2, k3, k4, cp, ts, te):
    # t: 采样时间点（中心点）
    # cp: 血浆输入函数（与t同长度）
    # ts, te: 每帧起止时间
    # 返回：每个时间点的组织浓度
    dt = te - ts
    n = len(t)
    Ct = np.zeros(n)
    # 解析解，卷积实现
    for i in range(n):
        # 对每一帧，积分输入函数
        integral = 0
        for j in range(i+1):
            tau = t[j]
            # impulse response function
            exp1 = np.exp(-(k2 + k3 + k4) * (t[i] - tau))
            exp2 = np.exp(-k4 * (t[i] - tau))
            h = K1 * ( (k3 + k4 - k2) / (k3 + k4 - k2 - k4) * exp1 + (k2 - k3 - k4) / (k2 - k3 - k4 - k4) * exp2 )
            integral += cp[j] * h * dt[j]
        Ct[i] = integral
    return Ct

# --- 拟合包装函数 ---
def fit_voxel_tac(td, tac, cp, ts, te):
    # 初始参数猜测
    p0 = [0.1, 0.1, 0.01, 0.01]
    bounds = ([0, 0, 0, 0], [10, 10, 1, 1])
    # 拟合时将cp, ts, te作为常量传递
    def model_func(t, K1, k2, k3, k4):
        return two_tissue_model(t, K1, k2, k3, k4, cp, ts, te)
    try:
        popt, _ = curve_fit(model_func, td, tac, p0=p0, bounds=bounds, maxfev=2000)
        fit_curve = model_func(td, *popt)
        return popt, fit_curve
    except Exception:
        return None, np.full_like(td, np.nan)

# --- 随机选取100个像素点 ---
np.random.seed(42)
total_voxels = n1 * n2 * n3
indices = np.random.choice(total_voxels, 100, replace=False)
coords = np.array(np.unravel_index(indices, (n1, n2, n3))).T

# --- 生成PDF ---
pdf_path = "Voxel_TAC_Fitting_Results.pdf"
with PdfPages(pdf_path) as pdf:
    for idx, (xi, yi, zi) in enumerate(coords):
        tac = data[xi, yi, zi, :]
        # 跳过全零或异常点
        if np.all(tac == 0) or np.any(np.isnan(tac)):
            continue
        popt, fit_curve = fit_voxel_tac(td.values, tac, cp.values, ts.values, te.values)
        plt.figure(figsize=(6, 4))
        plt.plot(td, tac, 'o', label='原始TAC')
        if popt is not None:
            plt.plot(td, fit_curve, '-', label='两房室模型拟合')
            plt.title(f'Voxel ({xi+1},{yi+1},{zi+1})\nK1={popt[0]:.3f}, k2={popt[1]:.3f}, k3={popt[2]:.3f}, k4={popt[3]:.3f}')
        else:
            plt.title(f'Voxel ({xi+1},{yi+1},{zi+1})\n拟合失败')
        plt.xlabel('Time (min)')
        plt.ylabel('Activity')
        plt.legend()
        plt.tight_layout()
        pdf.savefig()
        plt.close()
print(f"已保存PDF到: {pdf_path}")

