import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from scipy.optimize import curve_fit, nnls
from matplotlib.backends.backend_pdf import PdfPages
import time
import warnings
warnings.filterwarnings('ignore')

class PETAnalysis:
    """PET动态成像分析类"""
    
    def __init__(self, data_file, groundtruth_file):
        self.data_file = data_file
        self.groundtruth_file = groundtruth_file
        self.data = None
        self.td = None
        self.cp = None
        self.ts = None
        self.te = None
        self.n1, self.n2, self.n3, self.nt = 168, 168, 74, 45
        
    def load_data(self):
        """加载PET数据和时间信息"""
        print("正在加载数据...")
        
        # 加载动态PET数据
        nn = self.n1 * self.n2 * self.n3
        with open(self.data_file, "rb") as f:
            data_raw = np.fromfile(f, dtype=">f4", count=nn * self.nt)
        self.data = data_raw.reshape(self.n1, self.n2, self.n3, self.nt, order="F")
        
        # 加载时间信息和输入函数
        GT = pd.read_csv(self.groundtruth_file)
        self.ts = GT['ts']  # 起始时间
        self.te = GT['te']  # 结束时间
        self.td = (self.ts + self.te) / 2  # 中点时间
        self.cp = GT['tac4']  # 动脉输入函数
        
        print(f"数据加载完成: {self.n1}x{self.n2}x{self.n3}x{self.nt}")
        
    def two_tissue_model(self, t, K1, k2, k3, k4, cp, ts, te):
        dt = te - ts
        n = len(t)
        Ct = np.zeros(n)
        
        # 解析解，卷积实现
        for i in range(n):
            integral = 0
            for j in range(i+1):
                tau = t[j]
                # 冲激响应函数
                exp1 = np.exp(-(k2 + k3 + k4) * (t[i] - tau))
                exp2 = np.exp(-k4 * (t[i] - tau))
                h = K1 * ((k3 + k4 - k2) / (k3 + k4 - k2 - k4) * exp1 + 
                         (k2 - k3 - k4) / (k2 - k3 - k4 - k4) * exp2)
                integral += cp[j] * h * dt[j]
            Ct[i] = integral
        return Ct
    
    def fit_compartment_model(self, td, tac, cp, ts, te):
        # 初始参数猜测
        p0 = [0.1, 0.1, 0.01, 0.01]
        bounds = ([0, 0, 0, 0], [10, 10, 1, 1])
        
        # 拟合时将cp, ts, te作为常量传递
        def model_func(t, K1, k2, k3, k4):
            return self.two_tissue_model(t, K1, k2, k3, k4, cp, ts, te)
        
        try:
            popt, _ = curve_fit(model_func, td, tac, p0=p0, bounds=bounds, maxfev=2000)
            fit_curve = model_func(td, *popt)
            return popt, fit_curve
        except Exception:
            return None, np.full_like(td, np.nan)
    
    def spectral_analysis(self, td, tac, cp, ts, te, alpha_range=None):
        if alpha_range is None:
            # 默认alpha范围：从0.001到10，对数均匀分布
            alpha_values = np.logspace(-3, 1, 100)
        else:
            alpha_values = alpha_range
        
        n_time = len(td)
        n_alpha = len(alpha_values)
        
        # 构建基函数矩阵
        basis_matrix = np.zeros((n_time, n_alpha + 1))  # +1 for vascular component
        
        # 计算每个alpha对应的基函数
        for i, alpha in enumerate(alpha_values):
            for j in range(n_time):
                integral = 0
                for k in range(j + 1):
                    # 指数衰减的冲激响应函数
                    dt = te.iloc[k] - ts.iloc[k]
                    tau = td.iloc[k]
                    t_current = td.iloc[j]
                    
                    # 计算从tau到t_current的指数衰减
                    if t_current >= tau:
                        exp_decay = np.exp(-alpha * (t_current - tau))
                        integral += cp.iloc[k] * exp_decay * dt
                
                basis_matrix[j, i] = integral
        
        # 添加血管成分（无衰减）
        basis_matrix[:, -1] = cp.values
        
        # 使用非负最小二乘法求解
        try:
            spectrum_coeffs, residual = nnls(basis_matrix, tac)
            
            # 计算拟合曲线
            fit_curve = basis_matrix @ spectrum_coeffs
            
            # 提取频谱和血管成分
            spectrum = spectrum_coeffs[:-1]
            vascular_component = spectrum_coeffs[-1]
            
            # 计算等效K1（总流入速率常数）
            K1_equiv = np.sum(spectrum)
            
            return fit_curve, spectrum, alpha_values, K1_equiv, vascular_component, residual
        
        except Exception as e:
            print(f"谱分析拟合失败: {e}")
            return np.full_like(td, np.nan), None, alpha_values, None, None, None
    
    def select_valid_voxels(self, n_voxels=100):

        
        # 找到有效的体素（非零且非NaN）
        valid_mask = (np.any(self.data != 0, axis=3)) & (~np.any(np.isnan(self.data), axis=3))
        valid_indices = np.argwhere(valid_mask)
        
        print(f"找到 {len(valid_indices)} 个有效体素")
        
        if len(valid_indices) < n_voxels:
            raise ValueError(f"有效体素数量不足{n_voxels}，请检查数据！")
        
        # 随机选择指定数量的体素
        np.random.seed(42)  # 设置随机种子以确保结果可重现
        selected_indices = np.random.choice(len(valid_indices), n_voxels, replace=False)
        coords = valid_indices[selected_indices]
        
        return coords

    def compare_fitting_methods(self, coords, pdf_path="Spectral_vs_Compartment_Comparison.pdf"):


        plots_per_page = 9

        with PdfPages(pdf_path) as pdf:
            page_count = 0
            successful_fits = 0

            for page_start in range(0, len(coords), plots_per_page):
                fig, axes = plt.subplots(2, 3, figsize=(18, 12))
                axes = axes.flatten()

                for i in range(plots_per_page):
                    idx = page_start + i
                    if idx >= len(coords):
                        axes[i].axis('off')
                        continue

                    xi, yi, zi = coords[idx]
                    tac = self.data[xi, yi, zi, :]

                    # 跳过无效数据
                    if np.all(tac == 0) or np.any(np.isnan(tac)):
                        axes[i].axis('off')
                        continue

                    # 房室模型拟合
                    popt_compartment, fit_curve_compartment = self.fit_compartment_model(
                        self.td.values, tac, self.cp.values, self.ts.values, self.te.values
                    )

                    # 谱分析拟合
                    fit_curve_spectral, spectrum, alpha_values, K1_equiv, vascular_comp, residual = self.spectral_analysis(
                        self.td, tac, self.cp, self.ts, self.te
                    )

                    # 绘制比较图
                    axes[i].scatter(self.td.values, tac, color='gray', label='Original Data', s=25, alpha=0.8)

                    # 房室模型拟合线
                    if popt_compartment is not None:
                        axes[i].plot(self.td.values, fit_curve_compartment, color='red',
                                   label='Compartment Model', linewidth=2, linestyle='-')
                        compartment_info = f'K1={popt_compartment[0]:.3f}, k2={popt_compartment[1]:.3f}'
                    else:
                        compartment_info = 'Compartment: Failed'

                    # 谱分析拟合线
                    if not np.any(np.isnan(fit_curve_spectral)):
                        axes[i].plot(self.td.values, fit_curve_spectral, color='blue',
                                   label='Spectral Analysis', linewidth=2, linestyle='--')
                        spectral_info = f'K1_eq={K1_equiv:.3f}, Vasc={vascular_comp:.3f}'
                        successful_fits += 1
                    else:
                        spectral_info = 'Spectral: Failed'

                    # 设置标题和标签
                    title = f'Voxel ({xi+1},{yi+1},{zi+1})\n{compartment_info}\n{spectral_info}'
                    axes[i].set_title(title, fontsize=10)
                    axes[i].set_xlabel('Time (min)', fontsize=9)
                    axes[i].set_ylabel('Activity (kBq/cc)', fontsize=9)
                    axes[i].legend(fontsize=8)
                    axes[i].grid(True, alpha=0.3)

                    # 调整坐标轴范围
                    axes[i].set_xlim(0, max(self.td.values))
                    if np.max(tac) > 0:
                        axes[i].set_ylim(0, np.max(tac) * 1.1)

                plt.tight_layout()

                pdf.savefig(fig, dpi=150, bbox_inches='tight')
                plt.close(fig)
                page_count += 1

                # 显示进度
                if page_count % 5 == 0:
                    print(f"已处理 {min(page_start + plots_per_page, len(coords))} / {len(coords)} 个体素")


    def run_analysis(self, n_voxels=100, output_pdf="Spectral_vs_Compartment_Comparison.pdf"):
        start_time = time.time()

        # 加载数据
        self.load_data()

        # 选择有效体素
        coords = self.select_valid_voxels(n_voxels)

        # 进行比较分析
        self.compare_fitting_methods(coords, output_pdf)

        end_time = time.time()
        print(f"\n总耗时: {end_time - start_time:.2f} 秒")
        print(f"平均每个体素耗时: {(end_time - start_time) / n_voxels:.2f} 秒")


def main():
    # 文件路径配置
    data_file = "0100011.dyn.floatImage"  # PET动态数据文件
    groundtruth_file = "groundtruth.csv"  # 时间信息和输入函数文件

    # 创建分析对象
    pet_analysis = PETAnalysis(data_file, groundtruth_file)

    # 运行分析
    try:
        pet_analysis.run_analysis(
            n_voxels=100,  # 分析100个体素
            output_pdf="Spectral_vs_Compartment_Comparison.pdf"
        )

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        print("请检查数据文件路径和格式是否正确")




if __name__ == "__main__":
    main()

